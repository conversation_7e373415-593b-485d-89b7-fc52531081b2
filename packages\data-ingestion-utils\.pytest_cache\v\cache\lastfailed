{"tests/test_lens_direct_integration.py::TestLensDirectQueryBuilderIntegration::test_query_with_select": true, "tests/test_lens_direct_integration.py::TestLensDirectQueryBuilderIntegration::test_aggregation_query": true, "tests/test_lens_direct_integration.py::TestEnvironmentIntegration::test_environment_variables_valid": true, "tests/test_lens_direct_integration.py::TestFetchTableFunctionsIntegration::test_fetch_field_table_basic": true, "tests/test_lens_direct_metadata_provider.py::test_invalid_asset_type_validation": true, "tests/test_lens_direct_metadata_provider.py::test_get_company_asset_metadata_lazy_loading": true}