import axios from 'axios';
import logger from '../logger';

interface CachedToken {
    token: string;
    expiryTime: Date;
}

interface CachedEntitlement {
    hasAccess: boolean;
    expiryTime: Date;
}

interface EntitlementResponse {
    policies: unknown[];
}

const TOKEN_CACHE = new Map<string, CachedToken>();
const ENTITLEMENT_CACHE = new Map<string, CachedEntitlement>();

const TOKEN_EXPIRY_BUFFER_MINUTES = 10;
const ENTITLEMENT_CACHE_DURATION_MINUTES = 60;

export class EntitlementsService {
    private oauthUrl: string;
    private clientId: string;
    private clientSecret: string;
    private entitlementsApiUrl: string;
    private isConfigured: boolean;

    constructor() {
        this.oauthUrl = process.env.ENTITLEMENTS_OAUTH_URL || '';
        this.clientId = process.env.ENTITLEMENTS_CLIENT_ID || '';
        this.clientSecret = process.env.ENTITLEMENTS_CLIENT_SECRET || '';
        this.entitlementsApiUrl = process.env.ENTITLEMENTS_API_URL || '';

        this.isConfigured = !!(
            this.oauthUrl &&
            this.clientId &&
            this.clientSecret &&
            this.entitlementsApiUrl
        );

        if (!this.isConfigured) {
            logger.error(
                {
                    oauthUrl: !!this.oauthUrl,
                    clientId: !!this.clientId,
                    clientSecret: !!this.clientSecret,
                    entitlementsApiUrl: !!this.entitlementsApiUrl,
                },
                'Missing entitlements configuration',
            );
        }
    }

    clearCaches(): void {
        TOKEN_CACHE.clear();
        ENTITLEMENT_CACHE.clear();
    }

    private async getOAuthToken(): Promise<string | null> {
        if (!this.isConfigured) {
            logger.error('Entitlements service not configured');
            return null;
        }

        const cacheKey = `${this.clientId}:user.effective-role:read`;
        const currentTime = new Date();

        const cached = TOKEN_CACHE.get(cacheKey);
        if (cached && cached.expiryTime > currentTime) {
            const remainingMinutes =
                (cached.expiryTime.getTime() - currentTime.getTime()) /
                (1000 * 60);
            logger.debug(
                `Using cached OAuth token. Valid for ${remainingMinutes.toFixed(
                    1,
                )} more minutes`,
            );
            return cached.token;
        }

        try {
            const response = await axios.post(
                this.oauthUrl,
                new URLSearchParams({
                    grant_type: 'client_credentials',
                    scope: 'user.effective-role:read',
                }),
                {
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                        Authorization: `Basic ${Buffer.from(
                            `${this.clientId}:${this.clientSecret}`,
                        ).toString('base64')}`,
                    },
                    timeout: 10000,
                },
            );

            const { access_token, expires_in } = response.data;

            if (!access_token) {
                logger.error('No access_token in OAuth response');
                return null;
            }

            const expiryTime = new Date();
            const expiryMinutes = Math.max(
                expires_in / 60 - TOKEN_EXPIRY_BUFFER_MINUTES,
                1,
            );
            expiryTime.setMinutes(expiryTime.getMinutes() + expiryMinutes);

            TOKEN_CACHE.set(cacheKey, {
                token: access_token,
                expiryTime,
            });

            logger.debug('OAuth token retrieved and cached successfully');
            return access_token;
        } catch (error) {
            logger.error(
                error as Error,
                'Failed to get OAuth token for entitlements',
            );
            return null;
        }
    }

    async checkUserEntitlement(
        userId: string,
        feature: string,
    ): Promise<boolean> {
        if (!this.isConfigured) {
            logger.error('Entitlements service not configured');
            return false;
        }

        const cacheKey = `${userId}:${feature}`;
        const currentTime = new Date();

        const cached = ENTITLEMENT_CACHE.get(cacheKey);
        if (cached && cached.expiryTime > currentTime) {
            const remainingMinutes =
                (cached.expiryTime.getTime() - currentTime.getTime()) /
                (1000 * 60);
            logger.debug(
                { userId, feature },
                `Using cached entitlement. Valid for ${remainingMinutes.toFixed(
                    1,
                )} more minutes`,
            );
            return cached.hasAccess;
        }

        const token = await this.getOAuthToken();
        if (!token) {
            logger.error(
                { userId, feature },
                'Cannot check entitlements: failed to get OAuth token',
            );
            return false;
        }

        try {
            const url = `${this.entitlementsApiUrl}/api/v1/principals/${userId}/effective-role`;
            const response = await axios.get<EntitlementResponse>(url, {
                headers: {
                    Authorization: `Bearer ${token}`,
                    Accept: 'application/json',
                },
                params: { feature },
                timeout: 10000,
            });

            const hasAccess =
                response.data.policies && response.data.policies.length > 0;

            const expiryTime = new Date();
            expiryTime.setMinutes(
                expiryTime.getMinutes() + ENTITLEMENT_CACHE_DURATION_MINUTES,
            );

            ENTITLEMENT_CACHE.set(cacheKey, {
                hasAccess,
                expiryTime,
            });

            logger.info(
                {
                    userId,
                    feature,
                    hasAccess,
                    policiesCount: response.data.policies?.length || 0,
                },
                'Entitlement check completed',
            );

            return hasAccess;
        } catch (error) {
            logger.error(
                { error: error as Error, userId, feature },
                'Failed to check user entitlements',
            );
            return false;
        }
    }
}

export const entitlementsService = new EntitlementsService();
