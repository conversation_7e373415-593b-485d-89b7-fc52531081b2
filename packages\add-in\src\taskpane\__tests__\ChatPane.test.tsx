import React from "react";
import { render, screen, fireEvent } from "@testing-library/react";
import { FluentProvider, webLightTheme } from "@fluentui/react-components";
import { ChatPane } from "../ChatPane";

jest.mock("../StartOptions", () => ({
  StartOptions: ({ setSelectedMode }: { setSelectedMode: (mode: string) => void }) => (
    <div data-testid="start-options">
      <button onClick={() => setSelectedMode("valuations")}>Select Valuations</button>
    </div>
  ),
}));

jest.mock("../SelectedMode", () => ({
  SelectedMode: ({ selectedMode }: { selectedMode: string }) => (
    <div data-testid="selected-mode">Selected: {selectedMode}</div>
  ),
}));

jest.mock("../Footer", () => ({
  Footer: () => <div data-testid="footer">Footer</div>,
}));

jest.mock("../Icons", () => ({
  Icon24ArrowLeft: ({ className }: { className?: string }) => (
    <div data-testid="arrow-left-icon" className={className}>
      ←
    </div>
  ),
}));

const renderWithProvider = (component: React.ReactElement) => {
  return render(<FluentProvider theme={webLightTheme}>{component}</FluentProvider>);
};

describe("ChatPane", () => {
  it("renders StartOptions by default", () => {
    renderWithProvider(<ChatPane />);

    expect(screen.getByTestId("start-options")).toBeInTheDocument();
    expect(screen.queryByTestId("selected-mode")).not.toBeInTheDocument();
  });

  it("switches to SelectedMode when a mode is selected", () => {
    renderWithProvider(<ChatPane />);

    const selectButton = screen.getByText("Select Valuations");
    fireEvent.click(selectButton);

    expect(screen.getByTestId("selected-mode")).toBeInTheDocument();
    expect(screen.getByText("Selected: valuations")).toBeInTheDocument();
    expect(screen.queryByTestId("start-options")).not.toBeInTheDocument();
  });

  it("shows back button when mode is selected", () => {
    renderWithProvider(<ChatPane />);

    const selectButton = screen.getByText("Select Valuations");
    fireEvent.click(selectButton);

    expect(screen.getByText("Choose another workflow")).toBeInTheDocument();
    expect(screen.getByTestId("arrow-left-icon")).toBeInTheDocument();
  });

  it("returns to StartOptions when back button is clicked", () => {
    renderWithProvider(<ChatPane />);

    const selectButton = screen.getByText("Select Valuations");
    fireEvent.click(selectButton);

    const backButton = screen.getByText("Choose another workflow");
    fireEvent.click(backButton);

    expect(screen.getByTestId("start-options")).toBeInTheDocument();
    expect(screen.queryByTestId("selected-mode")).not.toBeInTheDocument();
  });

  it("renders the Footer component", () => {
    renderWithProvider(<ChatPane />);

    expect(screen.getByTestId("footer")).toBeInTheDocument();
  }); 
});
