import React from "react";
import { useStyles } from "../use-styles";
import { Button } from "@fluentui/react-components";
import { useLensDirectorAgent } from "./use-lens-director-agent";
import { AddRegular } from "@fluentui/react-icons";
import { Agent } from "../Agent";

const LoadLensDirectDataButton = ({ oDataUrl }) => {
    const { buttonIcon } = useStyles();
    const { loadLensDirectData, loadingLensDirectData } = useLensDirectorAgent();

    return (
        <Button disabled={loadingLensDirectData} onClick={() => loadLensDirectData(oDataUrl)}><AddRegular className={buttonIcon} /> Load data to a new sheet</Button>
    );
};

const LensDirectorAgent = () => {
    const { chatHistory, loading, handleChatMessageEntered, showSuggestedQuestions, suggestedQuestions } = useLensDirectorAgent();

    return (
        <Agent 
            howCanIHelpMessage="How can I help you with Lens Direct?"
            chatHistory={chatHistory}
            loading={loading}
            handleChatMessageEntered={handleChatMessageEntered}
            showSuggestedQuestions={showSuggestedQuestions}
            suggestedQuestions={suggestedQuestions}
            botMessageFooterComponentsCreator={table => [table && [<LoadLensDirectDataButton key="load-lens-data" oDataUrl={table}/>]]}
        />
    );
};

export { LensDirectorAgent };