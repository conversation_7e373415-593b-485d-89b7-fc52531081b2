import { Request, Response, NextFunction } from 'express';
import OktaJwtVerifier from '@okta/jwt-verifier';
import { createAuthMiddleware } from './auth-middleware';
import logger from '../logger';

jest.mock('@okta/jwt-verifier');
jest.mock('../logger', () => ({
    error: jest.fn(),
    warn: jest.fn(),
}));

const mockOktaJwtVerifier = OktaJwtVerifier as jest.MockedClass<
    typeof OktaJwtVerifier
>;
const mockLogger = logger as jest.Mocked<typeof logger>;

interface AuthenticatedRequest extends Request {
    user?: {
        email: string;
        id: string;
        accountId: string;
        internalUserEmail?: string;
    };
}

describe('createAuthMiddleware', () => {
    let req: Partial<AuthenticatedRequest>;
    let res: Partial<Response>;
    let next: NextFunction;
    let mockVerifyAccessToken: jest.Mock;

    beforeEach(() => {
        req = {
            headers: {},
            path: '/test-path',
            ip: '127.0.0.1',
        };
        res = {
            status: jest.fn().mockReturnThis(),
            json: jest.fn(),
        };
        next = jest.fn();
        mockVerifyAccessToken = jest.fn();

        mockOktaJwtVerifier.mockImplementation(
            () =>
                ({
                    verifyAccessToken: mockVerifyAccessToken,
                } as unknown as OktaJwtVerifier),
        );

        jest.clearAllMocks();
    });

    describe('when issuer or audience is missing', () => {
        it('should log error and return 401 when issuer is missing', async () => {
            const middleware = createAuthMiddleware(undefined, 'test-audience');

            await middleware(req as Request, res as Response, next);

            expect(mockLogger.error).toHaveBeenCalledWith(
                { issuer: undefined, audience: 'test-audience' },
                'Missing issuer or audience configuration',
            );
            expect(res.status).toHaveBeenCalledWith(401);
            expect(res.json).toHaveBeenCalledWith({ message: 'Unauthorized' });
            expect(next).not.toHaveBeenCalled();
        });

        it('should log error and return 401 when audience is missing', async () => {
            const middleware = createAuthMiddleware('test-issuer', undefined);

            await middleware(req as Request, res as Response, next);

            expect(mockLogger.error).toHaveBeenCalledWith(
                { issuer: 'test-issuer', audience: undefined },
                'Missing issuer or audience configuration',
            );
            expect(res.status).toHaveBeenCalledWith(401);
            expect(res.json).toHaveBeenCalledWith({ message: 'Unauthorized' });
            expect(next).not.toHaveBeenCalled();
        });
    });

    describe('when authorization header is missing or invalid', () => {
        it('should log warning and return 401 when authorization header is missing', async () => {
            const middleware = createAuthMiddleware(
                'test-issuer',
                'test-audience',
            );

            await middleware(req as Request, res as Response, next);

            expect(mockLogger.warn).toHaveBeenCalledWith(
                { path: '/test-path', ip: '127.0.0.1' },
                'Missing authorization header',
            );
            expect(res.status).toHaveBeenCalledWith(401);
            expect(res.json).toHaveBeenCalledWith({ message: 'Unauthorized' });
            expect(next).not.toHaveBeenCalled();
        });

        it('should log warning and return 401 when authorization header does not start with Bearer', async () => {
            req.headers = { authorization: 'Basic token123' };
            const middleware = createAuthMiddleware(
                'test-issuer',
                'test-audience',
            );

            await middleware(req as Request, res as Response, next);

            expect(mockLogger.warn).toHaveBeenCalledWith(
                { path: '/test-path', ip: '127.0.0.1' },
                'Missing authorization header',
            );
            expect(res.status).toHaveBeenCalledWith(401);
            expect(res.json).toHaveBeenCalledWith({ message: 'Unauthorized' });
            expect(next).not.toHaveBeenCalled();
        });
    });

    describe('when token verification fails', () => {
        it('should log warning and return 401 when token verification throws error', async () => {
            req.headers = { authorization: 'Bearer invalid-token' };
            const error = new Error('Token verification failed');
            mockVerifyAccessToken.mockRejectedValue(error);

            const middleware = createAuthMiddleware(
                'test-issuer',
                'test-audience',
            );

            await middleware(req as Request, res as Response, next);

            expect(mockLogger.warn).toHaveBeenCalledWith(
                { path: '/test-path', ip: '127.0.0.1', error },
                'Token verification failed',
            );
            expect(res.status).toHaveBeenCalledWith(401);
            expect(res.json).toHaveBeenCalledWith({ message: 'Unauthorized' });
            expect(next).not.toHaveBeenCalled();
        });
    });

    describe('when token verification succeeds', () => {
        it('should add external user to request and call next', async () => {
            req.headers = { authorization: 'Bearer valid-token' };
            const claims = {
                sub: '<EMAIL>',
                uid: 'user123',
                cid: 'company123',
            };
            mockVerifyAccessToken.mockResolvedValue({ claims });

            const middleware = createAuthMiddleware(
                'test-issuer',
                'test-audience',
            );

            await middleware(
                req as AuthenticatedRequest,
                res as Response,
                next,
            );

            expect(req.user).toEqual({
                email: '<EMAIL>',
                id: 'user123',
                accountId: 'company123',
            });
            expect(next).toHaveBeenCalled();
            expect(res.status).not.toHaveBeenCalled();
        });

        it('should add internal woodmac user with internalUserEmail field', async () => {
            req.headers = { authorization: 'Bearer valid-token' };
            const claims = {
                sub: '<EMAIL>',
                uid: 'user123',
                cid: 'company123',
            };
            mockVerifyAccessToken.mockResolvedValue({ claims });

            const middleware = createAuthMiddleware(
                'test-issuer',
                'test-audience',
            );

            await middleware(
                req as AuthenticatedRequest,
                res as Response,
                next,
            );

            expect(req.user).toEqual({
                email: '<EMAIL>',
                id: 'user123',
                accountId: 'company123',
                internalUserEmail: '<EMAIL>',
            });
            expect(next).toHaveBeenCalled();
        });

        it('should add internal woodmacad user with internalUserEmail field', async () => {
            req.headers = { authorization: 'Bearer valid-token' };
            const claims = {
                sub: '<EMAIL>',
                uid: 'user123',
                cid: 'company123',
            };
            mockVerifyAccessToken.mockResolvedValue({ claims });

            const middleware = createAuthMiddleware(
                'test-issuer',
                'test-audience',
            );

            await middleware(
                req as AuthenticatedRequest,
                res as Response,
                next,
            );

            expect(req.user).toEqual({
                email: '<EMAIL>',
                id: 'user123',
                accountId: 'company123',
                internalUserEmail: '<EMAIL>',
            });
            expect(next).toHaveBeenCalled();
        });

        it('should properly verify token with correct issuer and audience', async () => {
            req.headers = { authorization: 'Bearer test-token' };
            const claims = {
                sub: '<EMAIL>',
                uid: 'user123',
                cid: 'company123',
            };
            mockVerifyAccessToken.mockResolvedValue({ claims });

            const middleware = createAuthMiddleware(
                'test-issuer',
                'test-audience',
            );

            await middleware(
                req as AuthenticatedRequest,
                res as Response,
                next,
            );

            expect(mockOktaJwtVerifier).toHaveBeenCalledWith({
                issuer: 'test-issuer',
            });
            expect(mockVerifyAccessToken).toHaveBeenCalledWith(
                'test-token',
                'test-audience',
            );
        });
    });
});
