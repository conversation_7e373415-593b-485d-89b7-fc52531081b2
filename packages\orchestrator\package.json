{"name": "valuations-api-proxy", "version": "1.0.0", "type": "module", "scripts": {"api": "node --watch --env-file=.env dist/server.cjs", "test": "jest", "test:coverage": "jest --coverage", "lint": "eslint .", "build": "esbuild src/server.ts --platform=node --bundle --outfile=dist/server.cjs", "build:ci": "npm run build -- --sourcemap --minify", "build:watch": "npm run build -- --watch --sourcemap"}, "author": "", "license": "ISC", "description": "", "publishConfig": {"registry": "https://nexus.prod.woodmac.com/repository/npm-woodmac/"}, "dependencies": {"@aws-sdk/client-bedrock-agent": "^3.826.0", "@aws-sdk/client-bedrock-agent-runtime": "^3.699.0", "@okta/jwt-verifier": "^4.0.1", "@splunk/otel": "^3.1.2", "@types/dotenv": "^6.1.1", "@woodmac-sdk/logging": "^2.4.0", "axios": "^1.7.8", "cors": "^2.8.5", "dotenv": "^16.4.7", "express": "^4.21.1", "express-async-errors": "^3.1.1"}, "devDependencies": {"@eslint/js": "^9.27.0", "@types/cors": "^2.8.15", "@types/express": "^4.17.20", "@types/jest": "^29.5.14", "@types/node": "^20.8.10", "@types/supertest": "^6.0.2", "esbuild": "0.25.0", "eslint": "^9.27.0", "jest": "^29.7.0", "supertest": "^7.0.0", "ts-jest": "^29.2.6", "ts-node": "^10.9.2", "typescript": "^5.8.3", "typescript-eslint": "^8.32.1"}}