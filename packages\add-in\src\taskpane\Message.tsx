import React from "react";
import { Card, Skeleton, SkeletonItem, Text } from "@fluentui/react-components";
import { useStyles } from "./use-styles";
import { PreviewTable } from "./PreviewTable";
import { TableData } from "./chat-types";

const Message = ({ message, tableData, cardClassName, containerClassName, footer, loading }: { message?: string; tableData?: TableData; cardClassName: string; containerClassName: string; footer?: React.ReactNode; loading?: boolean; }) => {
    const { messageText } = useStyles();
    const [messagePartA, messagePartB] = message?.split("{{TABLE}}") ?? [];

    return (
        <div className={containerClassName}>
            <Card className={cardClassName} >
                {messagePartA && <Text className={messageText}>{messagePartA}</Text>}
                {tableData &&
                    <PreviewTable tableData={tableData} />
                }
                {messagePartB && <Text className={messageText}>{messagePartB}</Text>}
                {loading && (
                    <Skeleton appearance="opaque">
                        <SkeletonItem />
                    </Skeleton>
                )}
                {footer}
            </Card>
        </div>
    );
};


export { Message };
