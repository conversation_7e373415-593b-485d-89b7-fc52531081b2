import React from "react";
import { render, screen, fireEvent } from "@testing-library/react";
import { FluentProvider, webLightTheme } from "@fluentui/react-components";
import { WhatDoYouWantButton } from "../WhatDoYouWantButton";

const renderWithProvider = (component: React.ReactElement) => {
  return render(<FluentProvider theme={webLightTheme}>{component}</FluentProvider>);
};

const mockIcon = <div data-testid="test-icon">🔧</div>;

describe("WhatDoYouWantButton", () => {
  const mockOnClick = jest.fn();

  beforeEach(() => {
    mockOnClick.mockClear();
  });

  it("renders with header and text", () => {
    renderWithProvider(
      <WhatDoYouWantButton icon={mockIcon} header="Test Header" text="Test description text" onClick={mockOnClick} />
    );

    expect(screen.getByText("Test Header")).toBeInTheDocument();
    expect(screen.getByText("Test description text")).toBeInTheDocument();
    expect(screen.getByTestId("test-icon")).toBeInTheDocument();
  });

  it("calls onClick when button is clicked", () => {
    renderWithProvider(
      <WhatDoYouWantButton icon={mockIcon} header="Test Header" text="Test description text" onClick={mockOnClick} />
    );

    const button = screen.getByRole("button");
    fireEvent.click(button);

    expect(mockOnClick).toHaveBeenCalledTimes(1);
  });

  it("is disabled when disabled prop is true", () => {
    renderWithProvider(
      <WhatDoYouWantButton
        icon={mockIcon}
        header="Test Header"
        text="Test description text"
        disabled={true}
        onClick={mockOnClick}
      />
    );

    const button = screen.getByRole("button");
    expect(button).toBeDisabled();
  });

  it("is enabled when disabled prop is false", () => {
    renderWithProvider(
      <WhatDoYouWantButton
        icon={mockIcon}
        header="Test Header"
        text="Test description text"
        disabled={false}
        onClick={mockOnClick}
      />
    );

    const button = screen.getByRole("button");
    expect(button).not.toBeDisabled();
  });

  it("is enabled when disabled prop is not provided", () => {
    renderWithProvider(
      <WhatDoYouWantButton icon={mockIcon} header="Test Header" text="Test description text" onClick={mockOnClick} />
    );

    const button = screen.getByRole("button");
    expect(button).not.toBeDisabled();
  });

  it("does not call onClick when disabled and clicked", () => {
    renderWithProvider(
      <WhatDoYouWantButton
        icon={mockIcon}
        header="Test Header"
        text="Test description text"
        disabled={true}
        onClick={mockOnClick}
      />
    );

    const button = screen.getByRole("button");
    fireEvent.click(button);

    expect(mockOnClick).not.toHaveBeenCalled();
  });
});
