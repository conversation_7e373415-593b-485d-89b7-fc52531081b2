from lens_direct_utils import fetch_field_company_history_table, fetch_field_company_table, fetch_plant_company_table, fetch_pipeline_company_table, fetch_field_table, fetch_lng_plant_table, fetch_pipeline_table, get_static_list_upstream_companies, fetch_pipeline_company_history_table, fetch_plant_company_history_table
import concurrent.futures
import time
import threading
import sys

class LensDirectMetadataProvider:
    """
    A class to provide metadata from the Lens Direct OData service.
    """
    # Columns mapping for Assets
    FIELD_COLUMN_MAPPING = {
        "taxation": "field_taxation",
        "basin": "basin_name",
        "country_region": "country_region",
        "region": "region",
        "super_region": "super_region",
        "onshore_offshore_breakdown": "field_onshore_offshore_break",
        "primary_resource_theme": "field_primary_resource_theme",
        "prms_classified_breakdown": "field_prms_breakdown",
        "maturity": "field_maturity",
        "development_type": "field_development_type",
        "hydrocarbon_type": "field_hydrocarbon_type",
        "asset_commerciality": "field_commercial_technical",
        "operator": "field_operator",
        "discovery_date": "field_date_discovery",
        "first_capex_year": "field_year_capex_first",
        "production_end_year": "field_year_production_end",
        "partners": "field_partners",
    }

    PLANT_COLUMN_MAPPING = {
        #"taxation": "",
        "basin": "shipping_basin",
        "country_region": "lng_region",
        "region": "region",
        "super_region": "super_region",
        "onshore_offshore_breakdown": "onshore_offshore_tags",
        #"primary_resource_theme": "",
        #"prms_classified_breakdown": "",
        #"maturity": "",
        #"development_type": "",
        #"hydrocarbon_type": "",
        #"asset_commerciality": "",
        "operator": "plant_operator",
        #"discovery_date": "",
        #"first_capex_year": "",
        #"production_end_year": "",
        "partners": "plant_partners",
    }

    PIPELINE_COLUMN_MAPPING = {
        #"taxation": "",
        "basin": "basin_name",
        "country_region": "country_region",
        "region": "region",
        "super_region": "super_region",
        "onshore_offshore_breakdown": "pipeline_onshore_offshore_brk",
        #"primary_resource_theme": "",
        #"prms_classified_breakdown": "",
        #"maturity": "",
        #"development_type": "",
        "hydrocarbon_type": "pipeline_product_type",
        #"asset_commerciality": "",
        "operator": "pipeline_operator",
        #"discovery_date": "pipeline_year_fid",
        #"first_capex_year": "",
        "production_end_year": "pipeline_year_decommissioned",
        "partners": "pipeline_partners",
    }

    FIELD_COMPANY_COLUMN_MAPPING = {
        "subsidiary_name": "subsidiary_name",
        "company_interest_in_subsidiary": "company_subsid_interest",
        "operator_or_partner": "operator_partner",
        "asset_interest": "super_region",
    }


    PLANT_COMPANY_COLUMN_MAPPING = {
        "subsidiary_name": "subsidiary_name",
        "company_interest_in_subsidiary": "company_subsid_interest",
        "operator_or_partner": "operator_partner",
        "asset_interest": "super_region",
    }

    PIPELINE_COMPANY_COLUMN_MAPPING = {
        "subsidiary_name": "subsidiary_name",
        "company_interest_in_subsidiary": "company_subsid_interest",
        "operator_or_partner": "operator_partner",
        "asset_interest": "super_region",
    }


    def __init__(self, scenario: str = None, max_retries: int = 1):
        self.scenario = scenario
        self.max_retries = max_retries
        print(f"Initializing LensDirectMetadataProvider with scenario: {scenario}, max_retries: {max_retries}", flush=True)
        self.companies = get_static_list_upstream_companies()
        self.assets_not_found = {
            'FIELD': set(),
            'PLAY_COMPANY_GEM': set(),
            'PLANT': set(),
            'TRANSPORT': set(),
        }
        self.asset_dict = {
            'FIELD': {},
            'PLAY_COMPANY_GEM': {},
            'PLANT': {},
            'TRANSPORT': {},
        }
        # Initialize company asset dictionaries for all asset types
        self.company_asset_dict = {
            'FIELD': {},
            'PLAY_COMPANY_GEM': {},
            'PLANT': {},
            'TRANSPORT': {},
        }
        # String interning pools for memory optimization - dynamically created based on column mappings
        self._string_pools = {}
        self._not_found_lock = threading.Lock()
        self._load_tables()

    def _intern_string(self, value, pool):
        """
        Intern strings to reduce memory usage for repeated values.

        Args:
            value: The string value to intern (can be None)
            pool (dict): The string pool dictionary to use for interning

        Returns:
            The interned string or None if value was None
        """
        if value is None:
            return None
        if value not in pool:
            pool[value] = value
        return pool[value]

    def _load_table(self, table_name: str):
        """
        Load a specific metadata table
        
        Args:
            table_name (str): The name of the table to load.
        """
        match table_name:
            case 'field':
                self._load_field_table()
            case 'plant':
                self._load_plant_table()
            case 'pipeline':
                self._load_pipeline_table()
            case 'field_company_history':
                self._load_field_company_history_table()
            case 'plant_company_history':
                self._load_plant_company_history_table()
            case 'pipeline_company_history':
                self._load_pipeline_company_history_table()
            case _:
                raise ValueError(f"Unknown table name: {table_name}")
        

    def _load_tables(self):
        """
        Load the metadata tables from the Lens Direct API in parallel.
        """
        print(f"Starting to load tables for scenario: {self.scenario}", flush=True)
        start_time = time.time()
        tables = []
        if self.scenario == 'all_upstream_assets':
            # From field table are loaded FIELD and PLAY_COMPANY_GEM
            tables = ['field', 'plant', 'pipeline']
        elif self.scenario == 'all_upstream_companies':
            tables = ['field_company_history', 'plant_company_history', 'pipeline_company_history']
        else:
            raise ValueError(f"Unknown scenario: {self.scenario}")
        
        with concurrent.futures.ThreadPoolExecutor() as executor:
            futures = [executor.submit(self._load_table, table) for table in tables]
            for future in concurrent.futures.as_completed(futures):
                try:
                    future.result()
                except Exception as e:
                    print(f"Exiting. Error loading table: {e}", flush=True)
                    sys.exit(1)
        
        elapsed = time.time() - start_time
        print(f"Loaded tables in {elapsed:.2f} seconds", flush=True)
        self.print_total_loaded_metadata()

    def _load_field_table(self):
        """
        Load the field table from the Lens Direct API and store as a dict for O(1) access,
        keeping only the columns needed for FIELD_COLUMN_MAPPING.
        """
        required_field_cols = set(self.FIELD_COLUMN_MAPPING.values())
        
        print("Loading field table from Lens Direct API...", flush=True)
        field_table = fetch_field_table(columns=list(required_field_cols), filter="id_field_gem ne '' and id_valuation ne null", max_retries=self.max_retries)
        if not field_table:
            raise ValueError("Failed to load field table from Lens Direct API.")

        required_field_cols = set(self.FIELD_COLUMN_MAPPING.values())
        for asset_type in ['FIELD', 'PLAY_COMPANY_GEM']:
            self.asset_dict[asset_type] = {}
        for row in field_table:
            if 'id_valuation' in row and 'id_field_gem' in row and row['id_field_gem']:
                record = {k: row.get(v, None) for k, v in self.FIELD_COLUMN_MAPPING.items() if v in required_field_cols}
                if 'FIELD' in row.get('id_field_gem'):
                    asset_type = 'FIELD'
                elif 'PLAY_COMPANY_GEM' in row.get('id_field_gem'):
                    asset_type = 'PLAY_COMPANY_GEM'
                else:
                    print(f"Unknown field type in id_field_gem: {row['id_field_gem']}", flush=True)
                    continue
                asset_id = row['id_valuation']
                self._add_asset(asset_type, asset_id, record)
        print(f"Stored {len(self.asset_dict['FIELD'])} FIELD and {len(self.asset_dict['PLAY_COMPANY_GEM'])} PLAY_COMPANY_GEM records from Lens Direct metadata.", flush=True)

    def _load_plant_table(self):
        """
        Load the LNG plant table from the Lens Direct API and store as a dict for O(1) access,
        keeping only the columns needed for PLANT_COLUMN_MAPPING.
        """
        required_plant_cols = set(self.PLANT_COLUMN_MAPPING.values())
    
        print("Loading LNG plant table from Lens Direct API...", flush=True)
        # LNG Plant does not have the id_field_gem column
        lng_plant_table = fetch_lng_plant_table(columns=list(required_plant_cols), filter="id_valuation ne null and valuation_type eq 'PLANT'", max_retries=self.max_retries)

        if not lng_plant_table:
            raise ValueError("Failed to load LNG plant table from Lens Direct API.")

        required_plant_cols = set(self.PLANT_COLUMN_MAPPING.values())
        self.asset_dict['PLANT'] = {}
        for row in lng_plant_table:
            if 'id_valuation' in row:
                asset_id = row['id_valuation']
                record = {k: row.get(v, None) for k, v in self.PLANT_COLUMN_MAPPING.items() if v in required_plant_cols}
                self._add_asset('PLANT', asset_id, record)
        print(f"Stored {len(self.asset_dict['PLANT'])} LNG plant records from Lens Direct metadata.", flush=True)

    def _load_pipeline_table(self):
        """
        Load the pipeline table from the Lens Direct API and store as a dict for O(1) access,
        keeping only the columns needed for PIPELINE_COLUMN_MAPPING.
        """
        required_pipeline_cols = set(self.PIPELINE_COLUMN_MAPPING.values())
        
        print("Loading pipeline table from Lens Direct API...", flush=True)
        # Pipeline does not have the id_field_gem column
        pipeline_table = fetch_pipeline_table(columns=list(required_pipeline_cols), filter="id_valuation ne null and valuation_type eq 'TRANSPORT'", max_retries=self.max_retries)

        if not pipeline_table:
            raise ValueError("Failed to load pipeline table from Lens Direct API.")

        required_pipeline_cols = set(self.PIPELINE_COLUMN_MAPPING.values())
        self.asset_dict['TRANSPORT'] = {}
        for row in pipeline_table:
            if 'id_valuation' in row:
                asset_id = row['id_valuation']
                record = {k: row.get(v, None) for k, v in self.PIPELINE_COLUMN_MAPPING.items() if v in required_pipeline_cols}
                self._add_asset('TRANSPORT', asset_id, record)
        print(f"Stored {len(self.asset_dict['TRANSPORT'])} pipeline records from Lens Direct metadata.", flush=True)

    def _add_asset(self, asset_type: str, asset_id: int, record: dict):
        """
        Add an asset to the asset_dict, logging a warning if a duplicate ID is detected.
        The new record will overwrite any existing record with the same ID.
        
        Args:
            asset_type (str): The type of the asset.
            asset_id (int): The ID of the asset.
            record (dict): The asset record to store.
        """
        
        if asset_id in self.asset_dict[asset_type]:
            print(f"Duplicate asset id detected for type {asset_type}: {asset_id}")
        self.asset_dict[asset_type][asset_id] = record

    def _add_company_asset(self, asset_type: str, asset_id: int, company_id: int, record: dict):
        """
        Add a company asset to the company_asset_dict, logging a warning if a duplicate ID is detected.
        The new record will overwrite any existing record with the same ID.
        Uses a two-level dictionary structure: first level is asset_id, second level is company_id.
        Applies string interning to reduce memory usage for repeated string values.

        Args:
            asset_type (str): The type of the asset.
            asset_id (int): The ID of the asset.
            company_id (int): The ID of the company.
            record (dict): The asset record to store.
        """
        # Use setdefault to reduce dictionary lookups
        asset_dict = self.company_asset_dict.setdefault(asset_type, {})
        company_dict = asset_dict.setdefault(asset_id, {})

        # Apply string interning to reduce memory usage for repeated values
        # Create interned record dynamically based on the record keys
        interned_record = {}
        for key, value in record.items():
            # Create a string pool for each column key if it doesn't exist
            if key not in self._string_pools:
                self._string_pools[key] = {}
            interned_record[key] = self._intern_string(value, self._string_pools[key])

        # To-do: decide what to do with multiple subsidiaries rows for the same company/asset.
        #if company_id in company_dict:
        #    print(f"Duplicate company asset id detected for type {asset_type}, asset {asset_id}, company {company_id}")
        company_dict[company_id] = interned_record

    def get_asset_metadata(self, asset_id: int, asset_type: str):
        """
        Get metadata for a specific asset by asset_id and asset_type.
        
        Args:
            asset_id (int): The ID of the asset.
            asset_type (str): The type of the asset.
        
        Returns:
            dict: Metadata for the specified asset.
        """
        if self.scenario != 'all_upstream_assets':
            return {}

        if asset_id is None or asset_type is None:
            return {}
        
        if asset_type in self.asset_dict:
            if asset_id in self.asset_dict[asset_type]:
                return self.asset_dict[asset_type][asset_id]
            else:
                self._asset_not_found(asset_id, asset_type)
                return {}
        return {}
    
    def _asset_not_found(self, asset_id: int, asset_type: str):
        """
        Record an asset ID that was not found in the metadata.
        
        Args:
            asset_id (int): The ID of the asset.
            asset_type (str): The type of the asset.
        """
        with self._not_found_lock:  # Thread-safe access
            if asset_type not in self.assets_not_found:
                self.assets_not_found[asset_type] = set()
            
            if asset_id not in self.assets_not_found[asset_type]:
                print(f"Asset ID {asset_id} not found in {asset_type} Lens metadata.", flush=True)
                self.assets_not_found[asset_type].add(asset_id)

    def _company_asset_not_found(self, asset_id: int, asset_type: str, company_name: str, asset_name: str = None):
        """
        Record a company asset ID that was not found in the metadata.
        Uses the same assets_not_found dictionary since scenarios are exclusive.
        
        Args:
            asset_id (int): The ID of the asset.
            asset_type (str): The type of the asset.
            company_name (str): The name of the company.
        """
        with self._not_found_lock:  # Thread-safe access
            if asset_type not in self.assets_not_found:
                self.assets_not_found[asset_type] = set()
            
            # Create a composite key for asset-company combination
            asset_company_key = f"{asset_id}-{company_name}"
            
            # Only log and add if not already recorded
            if asset_company_key not in self.assets_not_found[asset_type]:
                print(f"Company Asset {f"({asset_name}) " if asset_name else ''}ID {asset_id}-{company_name} not found in {asset_type} Lens metadata.", flush=True)
                self.assets_not_found[asset_type].add(asset_company_key)

    def print_count_assets_not_found(self):
        """
        Print the count of assets not found in the metadata.
        Works for both asset and company-asset scenarios since they are exclusive.
        """
        for asset_type, ids in self.assets_not_found.items():
            print(f"Assets not found for {asset_type}: {len(ids)}", flush=True)

    def get_company_asset_metadata(self, asset_id: int, asset_type: str, company_name: str, asset_name: str = None):
        """
        Get metadata for a specific company asset by asset_id, asset_type, and company_name.
        Args:
            asset_id (int): The ID of the asset.
            asset_name (str, optional): The name of the asset.
            asset_type (str): The type of the asset.
            company_name (str): The name of the company.
            asset_name (str, optional): The name of the asset, just for logging purposes.
        Returns:
            dict: Metadata for the specified company asset.
        """
        if self.scenario != 'all_upstream_companies':
            return {}
        
        if asset_id is None or asset_type is None or company_name is None:
            return {}

        # Check if asset_type is supported and exists in company_asset_dict
        if asset_type in self.company_asset_dict:
            if asset_id in self.company_asset_dict[asset_type] and company_name in self.company_asset_dict[asset_type][asset_id]:
                return self.company_asset_dict[asset_type][asset_id][company_name]
            else:
                self._company_asset_not_found(asset_id, asset_type, company_name, asset_name)
                return {}
        else:
            print(f"Asset type {asset_type} not supported for company metadata.", flush=True)
            return {}
        
    def _get_company_batches_by_amount(self, amount_of_batches: int = 20):
        """
        Split self.companies into a specified number of batches, balancing the sum of asset_count in each batch.
        Returns a list of lists of company names.
        """
        companies_with_counts = [(name, info.get('asset_count', 1)) for name, info in self.companies.items()]
        companies_with_counts.sort(key=lambda x: x[1], reverse=True)
        batches = [[] for _ in range(amount_of_batches)]
        batch_totals = [0] * amount_of_batches
        for name, count in companies_with_counts:
            # Find the batch with the smallest total asset_count
            idx = batch_totals.index(min(batch_totals))
            batches[idx].append(name)
            batch_totals[idx] += count
        return batches
    
    def _load_company_history_table_generic(self, table_type: str, config: dict, amount_of_batches: int = 20, amount_of_parallel_batches: int = 4):
        """
        Generic method to load company history tables from the Lens Direct API in batches.
        Batches are balanced so the sum of asset_count is split approximately equally between amount_of_batches.
        Requests are made in parallel, up to amount_of_parallel_batches at a time.
        
        Args:
            table_type: Type of table to load ('field', 'pipeline', 'plant')
            config: Configuration dictionary containing fetch_function, column_mapping, static_filter, asset_types, and has_id_field_gem
            amount_of_batches: number of batches to split companies into (default: 20)
            amount_of_parallel_batches: number of parallel requests (default: 4)
        """
        
        print(f"Loading {table_type}_company_history table from Lens Direct API in {amount_of_batches} balanced batches with up to {amount_of_parallel_batches} parallel requests...", flush=True)
        
        required_cols = set(config['column_mapping'].values())
        company_batches = self._get_company_batches_by_amount(amount_of_batches)
        all_rows = []

        def fetch_batch(batch):
            batch_filter = self._build_company_name_filter(batch)
            filter_expr = f"({config['static_filter']}) and ({batch_filter})"
            return config['fetch_function'](
                columns=list(required_cols),
                filter=filter_expr,
                max_retries=self.max_retries
            )

        with concurrent.futures.ThreadPoolExecutor(max_workers=amount_of_parallel_batches) as executor:
            future_to_batch = {executor.submit(fetch_batch, batch): batch for batch in company_batches}
            for future in concurrent.futures.as_completed(future_to_batch):
                rows = future.result()
                if rows:
                    all_rows.extend(rows)

        if not all_rows:
            raise ValueError(f"Failed to load {table_type}_company_history table from Lens Direct API.")

        for row in all_rows:
            if 'id_valuation' in row and 'company_name' in row:
                if config['has_id_field_gem'] and ('id_field_gem' not in row or not row['id_field_gem']):
                    continue
                    
                record = {k: row.get(v, None) for k, v in config['column_mapping'].items() if v in required_cols}
                asset_id = row['id_valuation']
                company_name = row['company_name']

                if config['has_id_field_gem']:
                    if 'FIELD' in row.get('id_field_gem'):
                        asset_type = 'FIELD'
                    elif 'PLAY_COMPANY_GEM' in row.get('id_field_gem'):
                        asset_type = 'PLAY_COMPANY_GEM'
                    else:
                        print(f"Unknown field type in id_field_gem: {row['id_field_gem']}", flush=True)
                        continue
                else:
                    asset_type = config['asset_types'][0]

                self._add_company_asset(asset_type, asset_id, company_name, record)

        total_records = 0
        for asset_type in config['asset_types']:
            total_records += sum(len(companies) for companies in self.company_asset_dict[asset_type].values())
        
        print(f"Stored {total_records} {table_type} company records from Lens Direct metadata.", flush=True)
    
    def _load_field_company_history_table(self, amount_of_batches: int = 20, amount_of_parallel_batches: int = 4):
        """
        Load the field_company_history table from the Lens Direct API in batches, only for companies in self.companies.
        Batches are balanced so the sum of asset_count is split approximately equally between amount_of_batches.
        Requests are made in parallel, up to amount_of_parallel_batches at a time.
        amount_of_batches: number of batches to split companies into (default: 20)
        amount_of_parallel_batches: number of parallel requests (default: 4)
        """
        config = {
            'fetch_function': fetch_field_company_history_table,
            'column_mapping': self.FIELD_COMPANY_COLUMN_MAPPING,
            'static_filter': "field_gem_filename ne ''",
            'asset_types': ['FIELD', 'PLAY_COMPANY_GEM'],
            'has_id_field_gem': True
        }
        self._load_company_history_table_generic('field', config, amount_of_batches, amount_of_parallel_batches)

    def _load_pipeline_company_history_table(self, amount_of_batches: int = 20, amount_of_parallel_batches: int = 4):
        """
        Load the pipeline_company_history table from the Lens Direct API in batches, only for companies in self.companies.
        Batches are balanced so the sum of asset_count is split approximately equally between amount_of_batches.
        Requests are made in parallel, up to amount_of_parallel_batches at a time.
        """
        config = {
            'fetch_function': fetch_pipeline_company_history_table,
            'column_mapping': self.PIPELINE_COMPANY_COLUMN_MAPPING,
            'static_filter': "pipeline_gem_filename ne ''",
            'asset_types': ['TRANSPORT'],
            'has_id_field_gem': False
        }
        self._load_company_history_table_generic('pipeline', config, amount_of_batches, amount_of_parallel_batches)

    def _load_plant_company_history_table(self, amount_of_batches: int = 20, amount_of_parallel_batches: int = 4):
        """
        Load the plant_company_history table from the Lens Direct API in batches, only for companies in self.companies.
        Batches are balanced so the sum of asset_count is split approximately equally between amount_of_batches.
        Requests are made in parallel, up to amount_of_parallel_batches at a time.
        """
        config = {
            'fetch_function': fetch_plant_company_history_table,
            'column_mapping': self.PLANT_COMPANY_COLUMN_MAPPING,
            'static_filter': "plant_gem_filename ne ''",
            'asset_types': ['PLANT'],
            'has_id_field_gem': False
        }
        self._load_company_history_table_generic('plant', config, amount_of_batches, amount_of_parallel_batches)

    def print_total_loaded_metadata(self):
        """
        Print the total loaded metadata for the current scenario.
        For 'all_upstream_assets', print counts from self.asset_dict.
        For 'all_upstream_companies', print counts from self.company_asset_dict.
        """
        if self.scenario == 'all_upstream_assets':
            print("Total loaded asset metadata:", flush=True)
            for asset_type, assets in self.asset_dict.items():
                print(f"  {asset_type}: {len(assets)} records", flush=True)
        elif self.scenario == 'all_upstream_companies':
            print("Total loaded company-asset metadata:", flush=True)
            for asset_type, assets in self.company_asset_dict.items():
                total = sum(len(companies) for companies in assets.values())
                print(f"  {asset_type}: {total} records", flush=True)
            self._print_string_pool_stats()
        else:
            print(f"Unknown scenario: {self.scenario}", flush=True)

    def _print_string_pool_stats(self):
        """
        Print statistics about string interning pools to monitor memory optimization effectiveness.
        """
        if not self._string_pools:
            print("String interning pools: No pools created yet", flush=True)
            return

        print("String interning pool statistics:", flush=True)
        for column_name, pool in self._string_pools.items():
            print(f"  {column_name}: {len(pool)} unique values", flush=True)

    def _build_company_name_filter(self, company_names):
        """
        As the legacy Lens Direct API service (/query) does not support '&' or '%26' in their search strings,
        We need to build a filter string for company names according to the following rules:
        1. If a company name does not contain '&', use eq comparator.
        2. If a company name contains one or more '&', use startswith (first part), contains (middle parts), endswith (last part).
        The logical separator between different companies is 'or'.
        """
        filters = []
        for name in company_names:
            safe_name = name.replace("'", "''")
            parts = [p.strip() for p in safe_name.split('&')]
            if len(parts) == 1:
                # No '&' in name
                filters.append(f"company_name eq '{safe_name}'")
            else:
                # One or more '&' in name
                filter_expr = []
                if parts[0]:
                    filter_expr.append(f"startswith(company_name,'{parts[0]}')")
                for p in parts[1:-1]:
                    if p:
                        filter_expr.append(f"contains(company_name,'{p}')")
                if parts[-1]:
                    filter_expr.append(f"endswith(company_name,'{parts[-1]}')")

                filters.append(f"({' and '.join(filter_expr)})" if len(filter_expr) > 1 else (filter_expr[0] if filter_expr else ''))
        return ' or '.join(filters)