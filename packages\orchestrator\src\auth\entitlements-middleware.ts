import { NextFunction, Response } from 'express';
import logger from '../logger';
import { AuthenticatedRequest } from './auth-middleware';
import { entitlementsService } from './entitlements-service';

const sendForbiddenResponse = (res: Response) =>
    res.status(403).json({
        message: 'Access denied',
        error: 'Forbidden',
    });

export const createEntitlementsMiddleware =
    (feature: string) =>
    async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
        const user = req.user;

        if (!user?.id) {
            logger.warn(
                { path: req.path, user },
                'Entitlements check failed: no authenticated user ID',
            );
            sendForbiddenResponse(res);
            return;
        }

        try {
            const hasAccess = await entitlementsService.checkUserEntitlement(
                user.id,
                feature,
            );

            if (!hasAccess) {
                logger.warn(
                    {
                        userId: user.id,
                        email: user.email,
                        feature,
                        path: req.path,
                    },
                    'Entitlements check failed: user does not have required permissions',
                );
                sendForbiddenResponse(res);
                return;
            }

            logger.debug(
                {
                    userId: user.id,
                    email: user.email,
                    feature: feature,
                    path: req.path,
                },
                'Entitlements check passed',
            );

            next();
        } catch (error) {
            logger.error(
                {
                    error: error as Error,
                    userId: user.id,
                    path: req.path,
                },
                'Entitlements check error',
            );
            sendForbiddenResponse(res);
        }
    };

export const entitlementsMiddleware = createEntitlementsMiddleware(
    'lens:globaldatadiscovery:optimisation', // todo change this when we have a custom entitlement for this product
);
