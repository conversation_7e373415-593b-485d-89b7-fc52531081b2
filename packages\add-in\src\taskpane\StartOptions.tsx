import React from "react";
import { useFlags } from "launchdarkly-react-client-sdk";
import { BotMessage } from "./BotMessage";
import { WhatDoYouWantButton } from "./WhatDoYouWantButton";
import { useStyles } from "./use-styles";
import { ChatMode } from "./chat-types";
import { Icon24Valuation,Icon24Question,Icon24Data,Icon24Pencil } from "./Icons";

const StartOptions = ({ setSelectedMode}: { setSelectedMode: (selected: ChatMode ) => void }) => {
    const { messageArea } = useStyles();
    const { writtenContent, lensDirector } = useFlags();
    
    return (
        <>
            <div className={messageArea}>
                <BotMessage message="Welcome, where would you like to start today?" hideAiWarning />
                <WhatDoYouWantButton header="Valuations" text="Calculate valuations on assets" icon={<Icon24Valuation/>} onClick={() => setSelectedMode("valuations")}/>
                <WhatDoYouWantButton disabled={!lensDirector} header="Lens Direct" text="Explore and Query Lens Direct directly" icon={<Icon24Data/>} onClick={() => setSelectedMode("lensDirect")}/>
                <WhatDoYouWantButton disabled={!writtenContent} header="Written Content" text="Find and Summarize data from our long form reports archive" icon={<Icon24Pencil/>} onClick={() => setSelectedMode("writtenContent")}/>
                <WhatDoYouWantButton disabled header="Help me find Answers" text="Not sure what you want? I can help you find what you are looking for" icon={<Icon24Question/>} onClick={() => setSelectedMode("helpMeChoose")}/>
            </div>
        </>
    );
};

export { StartOptions }