import React from "react";
import { Text } from "@fluentui/react-components";
import { useWrittenContentAgent } from "./use-written-content-agent";
import { Agent } from "../Agent";

const WrittenContentAgent = () => {
    const { currentMessage, chatHistory, loading, handleChatMessageEntered } = useWrittenContentAgent();
    
    return (
        <Agent 
            howCanIHelpMessage="How can I help you with Asset Reports?"
            chatHistory={chatHistory}
            loading={loading}
            handleChatMessageEntered={handleChatMessageEntered}
            currentMessage={currentMessage}
            botMessageFooterComponentsCreator={additionalData => additionalData && [<Text style={{ whiteSpace: "pre-wrap" }} size={100}>{additionalData}</Text>]}
        />
    );
};

export { WrittenContentAgent };
