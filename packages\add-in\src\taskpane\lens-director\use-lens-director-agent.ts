import { useEffect, useState } from "react";
import { apiClient } from "../api-client";
import { ChatMessage } from "../chat-types";
import { useFlags } from "launchdarkly-react-client-sdk";

const loadLensDirectData = (setLoadingLensDirectData: (loading: boolean) => void) => async (oDataUrl: string) => {
    setLoadingLensDirectData(true);

    const { data } = await apiClient.post("lens-direct-data",
        {
            oDataUrl,
        },
    );

    const values = data.value;

    let tableHeadings: string[] = [];
    let tableRows: string[][] = [];

    if(values.length) {
        tableHeadings = Object.keys(values[0]);
        console.log({ tableHeadings });
    
        tableRows = values.reduce((rows: string[][], row: Record<string, string>) => [
            ...rows,
            tableHeadings.map(heading => row[heading]),
        ], [tableHeadings]);
    } else {
        tableHeadings = ["No data returned by query"];
        tableRows = [tableHeadings];
    }

    await Excel.run(async (context) => {
        const tableSheet = context.workbook.worksheets.add();
        tableSheet.activate();
        tableSheet.getUsedRange().clear();
        const firstCell = tableSheet.getRange("A1");
        firstCell.getAbsoluteResizedRange(tableRows.length, tableRows[0].length).values = tableRows;
        tableSheet.getUsedRange().format.autofitColumns();
        await context.sync();
        tableSheet.tables.add(tableSheet.getUsedRange(), true);
        await context.sync();
    });

    setLoadingLensDirectData(false);
};

const askLensDirector = async (message: string) => {
    const { data } = await apiClient.post("ask-lens-director",
        {
            prompt: message,
        },
    );

    return data;
};

const getSuggestedQuestions = async (setSuggestedQuestions: (questions: string[]) => void) => {
    const { data: { questions } } = await apiClient.get("lens-director/questions");
    setSuggestedQuestions(questions);
};

const useLensDirectorAgent = () => {
    const { suggestQuestions } = useFlags();
    const [loading, setLoading] = useState(false);
    const [loadingLensDirectData, setLoadingLensDirectData] = useState(false);
    const [chatHistory, setChatHistory] = useState<ChatMessage[]>([]);

    const [suggestedQuestions, setSuggestedQuestions] = useState([]);

    useEffect(() => {
        if(suggestQuestions) {
            getSuggestedQuestions(setSuggestedQuestions);
        }
    }, [suggestQuestions]);


    const handleChatMessageEntered = async (message: string) => {
        if (message.trim()) {
            setLoading(true);
            setChatHistory(curr => [...curr, {
                role: "user",
                message,
            }]);

            try {
                const data = await askLensDirector(message);

                console.log({ data });
                if (data.status !== "Success") {
                    console.error(data);
                    setChatHistory(curr => [...curr, {
                        role: "assistant",
                        message: "Sorry, something has gone wrong. Please ask me something else",
                    }]);
                } else {
                    setChatHistory(curr => [...curr, {
                        role: "assistant",
                        message: `${data.humanReadableResponse}, I have generated this odata query for you:\n\n${data.oDataBaseUrl}/${data.oDataQuery}`,
                        additionalData: `${data.oDataBaseUrl}/${data.oDataQuery}`,
                    }]);
                }
            } catch (err) {
                console.error(err);

                setChatHistory(curr => [...curr, {
                    role: "assistant",
                    message: "Sorry, something has gone wrong. Please ask me something else",
                }]);

            }

            setLoading(false);
        }
    };

    return {
        chatHistory,
        loading,
        handleChatMessageEntered,
        loadLensDirectData: loadLensDirectData(setLoadingLensDirectData),
        loadingLensDirectData,
        suggestedQuestions,
        showSuggestedQuestions: suggestQuestions && chatHistory.length === 0 && !loading,
    }
};

export { useLensDirectorAgent };
