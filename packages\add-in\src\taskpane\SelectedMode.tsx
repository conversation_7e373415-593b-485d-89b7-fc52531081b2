import React from "react";
import { ValuationsAgent } from "./valuations";
import { LensDirectorAgent } from "./lens-director";
import { WrittenContentAgent } from "./written-content";
import { ChatMode } from "./chat-types";

const NullMode = () => null;

const modes = {
  valuations: ValuationsAgent,
  lensDirect: LensDirectorAgent,
  writtenContent: WrittenContentAgent,
  helpMeChoose: NullMode,
}

const SelectedMode = ({ selectedMode }: { selectedMode: ChatMode }) => {
    const Mode = modes[selectedMode];

    return (
        <Mode />
    );
};

export { SelectedMode };
