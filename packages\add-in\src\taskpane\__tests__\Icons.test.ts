import { Icon24Valuation, Icon24Question, Icon24PlayAudio, Icon24<PERSON>encil, Icon24Data, Icon24ArrowLeft } from "../Icons";

describe("Icons", () => {
  it("exports Icon24Valuation with correct properties", () => {
    expect(Icon24Valuation).toBeDefined();
    expect(typeof Icon24Valuation).toBe("function");
  });

  it("exports Icon24Question with correct properties", () => {
    expect(Icon24Question).toBeDefined();
    expect(typeof Icon24Question).toBe("function");
  });

  it("exports Icon24PlayAudio with correct properties", () => {
    expect(Icon24PlayAudio).toBeDefined();
    expect(typeof Icon24PlayAudio).toBe("function");
  });

  it("exports Icon24Pencil with correct properties", () => {
    expect(Icon24Pencil).toBeDefined();
    expect(typeof Icon24Pencil).toBe("function");
  });

  it("exports Icon24Data with correct properties", () => {
    expect(Icon24Data).toBeDefined();
    expect(typeof Icon24Data).toBe("function");
  });

  it("exports Icon24ArrowLeft with correct properties", () => {
    expect(Icon24ArrowLeft).toBeDefined();
    expect(typeof Icon24ArrowLeft).toBe("function");
  });

  it("all icons should be React components", () => {
    const icons = [Icon24Valuation, Icon24Question, Icon24PlayAudio, Icon24Pencil, Icon24Data, Icon24ArrowLeft];

    icons.forEach((Icon) => {
      expect(typeof Icon).toBe("function");
      const instance = Icon({});
      expect(instance).toBeDefined();
      expect(typeof instance).toBe("object");
    });
  });
});
