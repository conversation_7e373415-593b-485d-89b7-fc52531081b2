import { CompoundButton } from "@fluentui/react-components";
import React from "react";
import { useStyles } from "./use-styles";

const WhatDoYouWantButton = ({ icon, header, text, disabled, onClick, }: {  icon; header: string; text: string; disabled?: boolean; onClick: () => void }) => {
    const { botMessageContainer, whatDoYouWantButton } = useStyles();

    return (
        <div className={botMessageContainer} >
            <CompoundButton disabled={disabled} size="medium" icon={icon} secondaryContent={text} className={whatDoYouWantButton} onClick={onClick} >
                {header}
            </CompoundButton>
        </div>
    )
};

export { WhatDoYouWantButton };

