import { useState } from "react";
import { ChatMessage } from "../chat-types";
import { apiClient } from "../api-client";

interface Reference {
    tag: number;
    title: string;
    section: {
        name: string;
    };
};

interface MessageBuffer {
    text: string[];
    references?: Reference[];
};

const parseStreamContent = (value: string) => {
  const eventRegex = /event: (data|end)/g;

    return value
        .split(eventRegex)
        .filter(Boolean)
        .map(chunk => {
            const dataLine = chunk.trim().replace('data: ', '');
            try {
                const parsed = JSON.parse(dataLine);
                return parsed;
            // eslint-disable-next-line @typescript-eslint/no-unused-vars
            } catch (e) {
                return [];
            }
        })
        .flat();
};

const processStreamValue = (value: string, messageBuffer: MessageBuffer) => {
  const match = parseStreamContent(value);

  if (match?.length) {
    match.forEach((data) => {
      if (data.type === "reference") {
        messageBuffer.references = data.content;
      } else {
        const incoming = data.content.join("");
        messageBuffer.text.push(incoming);
      }
    });
  }

  return messageBuffer;
};

const askWrittenContentBot = async (message: string, onResponseUpdated: (updated: string) => void) => {
  const response = await apiClient.post(
    `/ask-digital-content`,
    {
      messages: [
        {
          content: message,
          role: "user",
        },
      ],
    },
    {
      headers: {
        Accept: "text/event-stream",
      },
      responseType: "stream",
      adapter: "fetch",
    }
  );

  if (response.status >= 400) {
    if (response.headers["content-type"]?.includes("application/json")) {
      const jsonData = await response.data.text();
      const errorData = JSON.parse(jsonData);
      throw new Error(`API returned error: ${errorData.message} (status: ${response.status})`);
    } else {
      throw new Error(`API request failed with status: ${response.status}`);
    }
  }

  const reader = response.data.pipeThrough(new TextDecoderStream()).getReader();

  let messageBuffer: MessageBuffer = {
    text: [],
  };

  while (true) {
    const data = await reader.read();
    const { value, done } = data;

    if (done) {
      break;
    }

    messageBuffer = processStreamValue(value, messageBuffer);
    onResponseUpdated(messageBuffer.text.join(""));
  }

  return messageBuffer;
};

const byTag = ({ tag: tagA }: Reference, { tag: tagB }: Reference) => tagA - tagB;
const toFooterText = (references: Reference[] | undefined) => {
  if (!references || references.length === 0) return "";
  return `References:\n${references
    .sort(byTag)
    .map(({ tag, title, section: { name } }) => `${tag}: ${title}: ${name}`)
    .join("\n")}`;
};

const useWrittenContentAgent = () => {
  const [loading, setLoading] = useState(false);
  const [currentMessage, setCurrentMessage] = useState("");
  const [chatHistory, setChatHistory] = useState<ChatMessage[]>([]);

  const handleChatMessageEntered = async (message: string) => {
    console.log({ message });
    if (message.trim()) {
      setLoading(true);
      setChatHistory((curr) => [
        ...curr,
        {
          role: "user",
          message,
        },
      ]);

      try {
        const { text, references } = await askWrittenContentBot(message, setCurrentMessage);
        console.log({ text, references });

        setChatHistory((curr) => [
          ...curr,
          {
            role: "assistant",
            message: `${text.join("")}`,
            additionalData: toFooterText(references),
          },
        ]);
      } catch (err) {
        console.error(err);

        setChatHistory((curr) => [
          ...curr,
          {
            role: "assistant",
            message: "Sorry, something has gone wrong. Please ask me something else",
          },
        ]);
      }

      setCurrentMessage("");
      setLoading(false);
    }
  };

  return {
    loading,
    chatHistory,
    currentMessage,
    handleChatMessageEntered,
  };
};

export { useWrittenContentAgent };
