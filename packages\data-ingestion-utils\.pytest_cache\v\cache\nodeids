["tests/test_asset_json_flattener.py::test_combine_parquet_files_empty_list", "tests/test_asset_json_flattener.py::test_combine_parquet_files_full_output", "tests/test_asset_json_flattener.py::test_combine_parquet_files_no_timestamp", "tests/test_asset_json_flattener.py::test_combine_parquet_files_nonexistent_file", "tests/test_asset_json_flattener.py::test_extract_value_from_repeat_pattern_dict", "tests/test_asset_json_flattener.py::test_extract_value_from_repeat_pattern_invalid", "tests/test_asset_json_flattener.py::test_extract_value_from_repeat_pattern_list", "tests/test_asset_json_flattener.py::test_flatt_all_json_dir", "tests/test_asset_json_flattener.py::test_flatt_all_json_dir_integration", "tests/test_asset_json_flattener.py::test_flatten_countries", "tests/test_asset_json_flattener.py::test_flatten_countries_excludes_unsupported_types", "tests/test_asset_json_flattener.py::test_flatten_countries_returns_empty_dataframe_for_empty_input", "tests/test_asset_json_flattener.py::test_flatten_countries_with_scenario_name", "tests/test_asset_json_flattener.py::test_flatten_discrete_csv", "tests/test_asset_json_flattener.py::test_flatten_discrete_json_with_body", "tests/test_asset_json_flattener.py::test_flatten_discrete_json_with_companies", "tests/test_asset_json_flattener.py::test_flatten_discrete_json_with_output_file", "tests/test_asset_json_flattener.py::test_flatten_discrete_metrics_all_asset_types[FIELD-FIELD_COLUMN_MAPPING]", "tests/test_asset_json_flattener.py::test_flatten_discrete_metrics_all_asset_types[PLANT-PLANT_COLUMN_MAPPING]", "tests/test_asset_json_flattener.py::test_flatten_discrete_metrics_all_asset_types[PLAY_COMPANY_GEM-PLAY_COMPANY_GEM_COLUMN_MAPPING]", "tests/test_asset_json_flattener.py::test_flatten_discrete_metrics_all_asset_types[TRANSPORT-TRANSPORT_COLUMN_MAPPING]", "tests/test_asset_json_flattener.py::test_flatten_discrete_parquet", "tests/test_asset_json_flattener.py::test_flatten_discrete_parquet_replaces_no_production_with_na", "tests/test_asset_json_flattener.py::test_flatten_timeseries_csv", "tests/test_asset_json_flattener.py::test_flatten_timeseries_json_with_body", "tests/test_asset_json_flattener.py::test_flatten_timeseries_json_with_companies", "tests/test_asset_json_flattener.py::test_flatten_timeseries_metrics", "tests/test_asset_json_flattener.py::test_flatten_timeseries_metrics_all_asset_types[FIELD-FIELD_COLUMN_MAPPING]", "tests/test_asset_json_flattener.py::test_flatten_timeseries_metrics_all_asset_types[PLANT-PLANT_COLUMN_MAPPING]", "tests/test_asset_json_flattener.py::test_flatten_timeseries_metrics_all_asset_types[PLAY_COMPANY_GEM-PLAY_COMPANY_GEM_COLUMN_MAPPING]", "tests/test_asset_json_flattener.py::test_flatten_timeseries_metrics_all_asset_types[TRANSPORT-TRANSPORT_COLUMN_MAPPING]", "tests/test_asset_json_flattener.py::test_flatten_timeseries_metrics_unsupported_asset_type", "tests/test_asset_json_flattener.py::test_flatten_timeseries_metrics_with_company", "tests/test_asset_json_flattener.py::test_flatten_timeseries_parquet", "tests/test_asset_json_flattener.py::test_flatten_timeseries_parquet_replaces_no_production", "tests/test_lens_direct_integration.py::TestCompanyFunctionsIntegration::test_fetch_valuable_companies_basic", "tests/test_lens_direct_integration.py::TestEnvironmentIntegration::test_environment_variables_valid", "tests/test_lens_direct_integration.py::TestErrorHandlingIntegration::test_invalid_endpoint", "tests/test_lens_direct_integration.py::TestErrorHandlingIntegration::test_invalid_filter", "tests/test_lens_direct_integration.py::TestErrorHandlingIntegration::test_network_retry_behavior", "tests/test_lens_direct_integration.py::TestFetchTableFunctionsIntegration::test_fetch_field_table_basic", "tests/test_lens_direct_integration.py::TestFetchTableFunctionsIntegration::test_fetch_lng_plant_table_basic", "tests/test_lens_direct_integration.py::TestFetchTableFunctionsIntegration::test_fetch_pipeline_table_basic", "tests/test_lens_direct_integration.py::TestFetchTableFunctionsIntegration::test_fetch_table_no_params", "tests/test_lens_direct_integration.py::TestLensDirectQueryBuilderIntegration::test_aggregation_query", "tests/test_lens_direct_integration.py::TestLensDirectQueryBuilderIntegration::test_basic_query_execution", "tests/test_lens_direct_integration.py::TestLensDirectQueryBuilderIntegration::test_pagination_handling", "tests/test_lens_direct_integration.py::TestLensDirectQueryBuilderIntegration::test_query_with_filter", "tests/test_lens_direct_integration.py::TestLensDirectQueryBuilderIntegration::test_query_with_order_by", "tests/test_lens_direct_integration.py::TestLensDirectQueryBuilderIntegration::test_query_with_select", "tests/test_lens_direct_metadata_provider.py::test_add_asset_duplicate_detection", "tests/test_lens_direct_metadata_provider.py::test_add_asset_invalid_type", "tests/test_lens_direct_metadata_provider.py::test_asset_not_found_tracking", "tests/test_lens_direct_metadata_provider.py::test_build_company_name_filter_cases", "tests/test_lens_direct_metadata_provider.py::test_company_data_cache", "tests/test_lens_direct_metadata_provider.py::test_duplicate_asset_no_exception_raised", "tests/test_lens_direct_metadata_provider.py::test_field_metadata", "tests/test_lens_direct_metadata_provider.py::test_get_asset_metadata_edge_cases[123-None]", "tests/test_lens_direct_metadata_provider.py::test_get_asset_metadata_edge_cases[123-UNKNOWN_TYPE]", "tests/test_lens_direct_metadata_provider.py::test_get_asset_metadata_edge_cases[None-FIELD]", "tests/test_lens_direct_metadata_provider.py::test_get_asset_metadata_edge_cases[None-None]", "tests/test_lens_direct_metadata_provider.py::test_get_company_asset_metadata_lazy_loading", "tests/test_lens_direct_metadata_provider.py::test_initialization_with_and_without_scenario", "tests/test_lens_direct_metadata_provider.py::test_invalid_asset_type_validation", "tests/test_lens_direct_metadata_provider.py::test_lazy_loading_initialization", "tests/test_lens_direct_metadata_provider.py::test_memory_cleanup_methods", "tests/test_lens_direct_metadata_provider.py::test_on_demand_company_data_fetching", "tests/test_lens_direct_metadata_provider.py::test_pipeline_metadata", "tests/test_lens_direct_metadata_provider.py::test_plant_metadata", "tests/test_lens_direct_metadata_provider.py::test_play_company_gem_metadata", "tests/test_lens_direct_metadata_provider.py::test_print_count_assets_not_found", "tests/test_lens_direct_metadata_provider.py::test_print_total_loaded_metadata_lazy_loading", "tests/test_lens_direct_metadata_provider.py::test_scenario_not_all_upstream_assets", "tests/test_lens_direct_utils.py::TestCompanyFunctions::test_batch_companies_by_asset_count_max_companies_limit", "tests/test_lens_direct_utils.py::TestCompanyFunctions::test_batch_companies_by_asset_count_multiple_batches", "tests/test_lens_direct_utils.py::TestCompanyFunctions::test_batch_companies_by_asset_count_single_batch", "tests/test_lens_direct_utils.py::TestCompanyFunctions::test_convert_batches_to_filters", "tests/test_lens_direct_utils.py::TestCompanyFunctions::test_fetch_valuable_companies", "tests/test_lens_direct_utils.py::TestCompanyFunctions::test_get_all_company_filter_batches", "tests/test_lens_direct_utils.py::TestCompanyFunctions::test_read_companies_from_csv", "tests/test_lens_direct_utils.py::TestEdgeCases::test_batch_companies_empty_map", "tests/test_lens_direct_utils.py::TestEdgeCases::test_build_url_with_apply_and_other_params", "tests/test_lens_direct_utils.py::TestEdgeCases::test_convert_batches_empty_list", "tests/test_lens_direct_utils.py::TestEdgeCases::test_request_with_retryable_status_codes", "tests/test_lens_direct_utils.py::TestEdgeCases::test_with_aggregation_empty_lists", "tests/test_lens_direct_utils.py::TestEnvironmentFunctions::test_get_lens_direct_url_and_token_missing_key", "tests/test_lens_direct_utils.py::TestEnvironmentFunctions::test_get_lens_direct_url_and_token_missing_url", "tests/test_lens_direct_utils.py::TestEnvironmentFunctions::test_get_lens_direct_url_and_token_success", "tests/test_lens_direct_utils.py::TestFetchTableFunctions::test_fetch_field_table", "tests/test_lens_direct_utils.py::TestFetchTableFunctions::test_fetch_lng_plant_table", "tests/test_lens_direct_utils.py::TestFetchTableFunctions::test_fetch_pipeline_table", "tests/test_lens_direct_utils.py::TestFetchTableFunctions::test_fetch_table_no_columns_no_filter", "tests/test_lens_direct_utils.py::TestLensDirectQueryBuilder::test_build_url_empty", "tests/test_lens_direct_utils.py::TestLensDirectQueryBuilder::test_build_url_with_all_params", "tests/test_lens_direct_utils.py::TestLensDirectQueryBuilder::test_build_url_with_filter", "tests/test_lens_direct_utils.py::TestLensDirectQueryBuilder::test_build_url_with_select", "tests/test_lens_direct_utils.py::TestLensDirectQueryBuilder::test_execute_multiple_pages", "tests/test_lens_direct_utils.py::TestLensDirectQueryBuilder::test_execute_single_page", "tests/test_lens_direct_utils.py::TestLensDirectQueryBuilder::test_init", "tests/test_lens_direct_utils.py::TestLensDirectQueryBuilder::test_request_http_error_no_retry", "tests/test_lens_direct_utils.py::TestLensDirectQueryBuilder::test_request_network_error_retry_exhausted", "tests/test_lens_direct_utils.py::TestLensDirectQueryBuilder::test_request_retry_success", "tests/test_lens_direct_utils.py::TestLensDirectQueryBuilder::test_request_success", "tests/test_lens_direct_utils.py::TestLensDirectQueryBuilder::test_with_aggregation", "tests/test_lens_direct_utils.py::TestLensDirectQueryBuilder::test_with_filter", "tests/test_lens_direct_utils.py::TestLensDirectQueryBuilder::test_with_order_by", "tests/test_lens_direct_utils.py::TestLensDirectQueryBuilder::test_with_select", "tests/test_lens_direct_utils.py::TestLensDirectQueryBuilder::test_with_top", "tests/test_utils.py::test_to_snake_case_acronyms", "tests/test_utils.py::test_to_snake_case_camel_case", "tests/test_utils.py::test_to_snake_case_combined_patterns", "tests/test_utils.py::test_to_snake_case_edge_cases", "tests/test_utils.py::test_to_snake_case_hyphens_parentheses", "tests/test_utils.py::test_to_snake_case_hyphens_slashes", "tests/test_utils.py::test_to_snake_case_pascal_case", "tests/test_utils.py::test_to_snake_case_spaces"]