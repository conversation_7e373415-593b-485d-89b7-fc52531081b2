import React from "react";
import { render, screen } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import { WrittenContentAgent } from "../WrittenContentAgent";

const mockHandleChatMessageEntered = jest.fn();

jest.mock("../use-written-content-agent", () => ({
  useWrittenContentAgent: () => ({
    currentMessage: "Currently generating response...",
    chatHistory: [
      { role: "user", message: "Write me a report" },
      {
        role: "assistant",
        message: "Here is your asset report",
        additionalData: "References:\n1: Document A: Section 1\n2: Document B: Section 2",
      },
    ],
    loading: true,
    handleChatMessageEntered: mockHandleChatMessageEntered,
  }),
}));

jest.mock("../../Agent", () => ({
  Agent: ({
    howCanIHelpMessage,
    chatHistory,
    loading,
    handleChatMessageEntered,
    currentMessage,
    botMessageFooterComponentsCreator,
  }: any) => {
    const messageWithAdditionalData = chatHistory.find((msg: any) => msg.additionalData);

    return (
      <div data-testid="agent">
        <div data-testid="help-message">{howCanIHelpMessage}</div>
        <div data-testid="loading-state">{loading ? "Loading" : "Not Loading"}</div>
        <div data-testid="current-message">{currentMessage || "No current message"}</div>
        <div data-testid="chat-history-length">{chatHistory.length}</div>
        <button data-testid="handle-message" onClick={() => handleChatMessageEntered("Test message")}>
          Send Message
        </button>
        {messageWithAdditionalData && (
          <div data-testid="footer-components">
            {botMessageFooterComponentsCreator(messageWithAdditionalData.additionalData).map((component: any, index: number) => (
              <div key={index} data-testid={`footer-component-${index}`}>
                {component}
              </div>
            ))}
          </div>
        )}
      </div>
    );
  },
}));

jest.mock("@fluentui/react-components", () => ({
  Text: ({ children, style, size }: any) => (
    <span data-testid="fluent-text" style={style} data-size={size}>
      {children}
    </span>
  ),
}));

describe("WrittenContentAgent", () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("renders with correct help message", () => {
    render(<WrittenContentAgent />);

    expect(screen.getByTestId("help-message")).toHaveTextContent("How can I help you with Asset Reports?");
  });

  it("passes current message to Agent component", () => {
    render(<WrittenContentAgent />);

    expect(screen.getByTestId("current-message")).toHaveTextContent("Currently generating response...");
  });

  it("passes chat history to Agent component", () => {
    render(<WrittenContentAgent />);

    expect(screen.getByTestId("chat-history-length")).toHaveTextContent("2");
  });

  it("passes loading state to Agent component", () => {
    render(<WrittenContentAgent />);

    expect(screen.getByTestId("loading-state")).toHaveTextContent("Loading");
  });

  it("handles chat message entry", async () => {
    const user = userEvent.setup();
    render(<WrittenContentAgent />);

    const sendButton = screen.getByTestId("handle-message");
    await user.click(sendButton);

    expect(mockHandleChatMessageEntered).toHaveBeenCalledWith("Test message");
  });

  it("creates footer components for additional data", () => {
    render(<WrittenContentAgent />);

    const footerComponents = screen.getByTestId("footer-components");
    expect(footerComponents).toBeInTheDocument();

    const component = screen.getByTestId("footer-component-0");
    expect(component).toBeInTheDocument();

    const textComponent = screen.getByTestId("fluent-text");
    expect(textComponent).toBeInTheDocument();
    expect(textComponent).toHaveTextContent("References: 1: Document A: Section 1 2: Document B: Section 2");
  });

  it("renders Text component with correct styles and size", () => {
    render(<WrittenContentAgent />);

    const textComponent = screen.getByTestId("fluent-text");
    expect(textComponent).toHaveStyle({ whiteSpace: "pre-wrap" });
    expect(textComponent).toHaveAttribute("data-size", "100");
  });

  it("integrates all hook values correctly", () => {
    render(<WrittenContentAgent />);

    expect(screen.getByTestId("agent")).toBeInTheDocument();
    expect(screen.getByTestId("help-message")).toHaveTextContent("How can I help you with Asset Reports?");
    expect(screen.getByTestId("loading-state")).toHaveTextContent("Loading");
    expect(screen.getByTestId("current-message")).toHaveTextContent("Currently generating response...");
    expect(screen.getByTestId("chat-history-length")).toHaveTextContent("2");
  });
});

describe("WrittenContentAgent footerComponentsCreator", () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("returns false when additionalData is falsy", () => {
    const footerComponentsCreator = (additionalData: any) => additionalData && [<div>Footer Content</div>];

    expect(footerComponentsCreator(null)).toBeFalsy();
    expect(footerComponentsCreator(undefined)).toBeFalsy();
    expect(footerComponentsCreator("")).toBeFalsy();
  });

  it("creates Text component for valid additional data", () => {
    render(<WrittenContentAgent />);

    const footerComponents = screen.getByTestId("footer-components");
    const textComponent = screen.getByTestId("fluent-text");

    expect(footerComponents).toBeInTheDocument();
    expect(textComponent).toBeInTheDocument();
    expect(textComponent).toHaveTextContent("References: 1: Document A: Section 1 2: Document B: Section 2");
    expect(textComponent).toHaveStyle({ whiteSpace: "pre-wrap" });
    expect(textComponent).toHaveAttribute("data-size", "100");
  });
});

describe("WrittenContentAgent with different states", () => {
  beforeEach(() => {
    jest.resetModules();
  });

  it("renders correctly when not loading", () => {
    jest.doMock("../use-written-content-agent", () => ({
      useWrittenContentAgent: () => ({
        currentMessage: "",
        chatHistory: [{ role: "user", message: "Hello" }],
        loading: false,
        handleChatMessageEntered: mockHandleChatMessageEntered,
      }),
    }));

    const { WrittenContentAgent: TestComponent } = require("../WrittenContentAgent");
    render(<TestComponent />);

    expect(screen.getByTestId("loading-state")).toHaveTextContent("Not Loading");
    expect(screen.getByTestId("current-message")).toHaveTextContent("No current message");
  });

  it("renders correctly with empty chat history", () => {
    jest.doMock("../use-written-content-agent", () => ({
      useWrittenContentAgent: () => ({
        currentMessage: "",
        chatHistory: [],
        loading: false,
        handleChatMessageEntered: mockHandleChatMessageEntered,
      }),
    }));

    const { WrittenContentAgent: TestComponent } = require("../WrittenContentAgent");
    render(<TestComponent />);

    expect(screen.getByTestId("chat-history-length")).toHaveTextContent("0");
    expect(screen.queryByTestId("footer-components")).not.toBeInTheDocument();
  });

  it("renders correctly without additional data", () => {
    jest.doMock("../use-written-content-agent", () => ({
      useWrittenContentAgent: () => ({
        currentMessage: "Some message",
        chatHistory: [
          { role: "user", message: "Question" },
          { role: "assistant", message: "Answer without additional data" },
        ],
        loading: false,
        handleChatMessageEntered: mockHandleChatMessageEntered,
      }),
    }));

    const { WrittenContentAgent: TestComponent } = require("../WrittenContentAgent");
    render(<TestComponent />);

    expect(screen.getByTestId("chat-history-length")).toHaveTextContent("2");
    expect(screen.queryByTestId("footer-components")).not.toBeInTheDocument();
  });
});
