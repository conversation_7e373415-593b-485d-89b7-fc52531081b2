import axios from 'axios';
import { EntitlementsService } from './entitlements-service';

jest.mock('axios');
const mockedAxios = axios as jest.Mocked<typeof axios>;

jest.mock('../logger', () => ({
    debug: jest.fn(),
    info: jest.fn(),
    error: jest.fn(),
    warn: jest.fn(),
}));

describe('EntitlementsService', () => {
    let service: EntitlementsService;
    const mockEnv = {
        ENTITLEMENTS_OAUTH_URL: 'https://oauth.example.com/token',
        ENTITLEMENTS_CLIENT_ID: 'test-client-id',
        ENTITLEMENTS_CLIENT_SECRET: 'test-client-secret',
        ENTITLEMENTS_API_URL: 'https://entitlements.example.com',
    };

    beforeEach(() => {
        jest.clearAllMocks();
        Object.assign(process.env, mockEnv);
        service = new EntitlementsService();
        service.clearCaches();
    });

    afterEach(() => {
        process.env.ENTITLEMENTS_OAUTH_URL = undefined;
        process.env.ENTITLEMENTS_CLIENT_ID = undefined;
        process.env.ENTITLEMENTS_CLIENT_SECRET = undefined;
        process.env.ENTITLEMENTS_API_URL = undefined;
    });

    describe('checkUserEntitlement', () => {
        it('should return true when user has entitlements', async () => {
            mockedAxios.post.mockResolvedValueOnce({
                data: {
                    access_token: 'test-token',
                    expires_in: 3600,
                },
            });

            mockedAxios.get.mockResolvedValueOnce({
                data: {
                    policies: [{ id: 1 }, { id: 2 }],
                },
            });

            const result = await service.checkUserEntitlement(
                'user123',
                'lens:globaldatadiscovery:optimisation',
            );

            expect(result).toBe(true);
            expect(mockedAxios.post).toHaveBeenCalledWith(
                'https://oauth.example.com/token',
                expect.any(URLSearchParams),
                expect.objectContaining({
                    headers: expect.objectContaining({
                        Authorization: expect.stringContaining('Basic'),
                    }),
                }),
            );
            expect(mockedAxios.get).toHaveBeenCalledWith(
                'https://entitlements.example.com/api/v1/principals/user123/effective-role',
                expect.objectContaining({
                    headers: expect.objectContaining({
                        Authorization: 'Bearer test-token',
                    }),
                    params: {
                        feature: 'lens:globaldatadiscovery:optimisation',
                    },
                }),
            );
        });

        it('should return false when user has no entitlements', async () => {
            mockedAxios.post.mockResolvedValueOnce({
                data: {
                    access_token: 'test-token',
                    expires_in: 3600,
                },
            });

            mockedAxios.get.mockResolvedValueOnce({
                data: {
                    policies: [],
                },
            });

            const result = await service.checkUserEntitlement(
                'user123',
                'lens:globaldatadiscovery:optimisation',
            );

            expect(result).toBe(false);
        });

        it('should return false when OAuth token request fails', async () => {
            mockedAxios.post.mockRejectedValueOnce(new Error('OAuth failed'));

            const result = await service.checkUserEntitlement(
                'user123',
                'lens:globaldatadiscovery:optimisation',
            );

            expect(result).toBe(false);
        });

        it('should return false when entitlements API call fails', async () => {
            mockedAxios.post.mockResolvedValueOnce({
                data: {
                    access_token: 'test-token',
                    expires_in: 3600,
                },
            });

            mockedAxios.get.mockRejectedValueOnce(new Error('API failed'));

            const result = await service.checkUserEntitlement(
                'user123',
                'lens:globaldatadiscovery:optimisation',
            );

            expect(result).toBe(false);
        });

        it('should use custom feature parameter', async () => {
            mockedAxios.post.mockResolvedValueOnce({
                data: {
                    access_token: 'test-token',
                    expires_in: 3600,
                },
            });

            mockedAxios.get.mockResolvedValueOnce({
                data: {
                    policies: [{ id: 1 }],
                },
            });

            await service.checkUserEntitlement('user123', 'custom:feature');

            expect(mockedAxios.get).toHaveBeenCalledWith(
                expect.any(String),
                expect.objectContaining({
                    params: { feature: 'custom:feature' },
                }),
            );
        });
    });
});
