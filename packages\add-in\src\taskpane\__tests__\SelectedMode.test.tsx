import React from "react";
import { render } from "@testing-library/react";
import { SelectedMode } from "../SelectedMode";

jest.mock("../valuations", () => ({
  ValuationsAgent: () => <div>Valuations Agent</div>,
}));

jest.mock("../lens-director", () => ({
  LensDirectorAgent: () => <div>Lens Director Agent</div>,
}));

jest.mock("../written-content", () => ({
  WrittenContentAgent: () => <div>Written Content Agent</div>,
}));

describe("SelectedMode", () => {
  it("renders ValuationsAgent when selectedMode is valuations", () => {
    const { getByText } = render(<SelectedMode selectedMode="valuations" />);
    expect(getByText("Valuations Agent")).toBeInTheDocument();
  });

  it("renders LensDirectorAgent when selectedMode is lensDirect", () => {
    const { getByText } = render(<SelectedMode selectedMode="lensDirect" />);
    expect(getByText("Lens Director Agent")).toBeInTheDocument();
  });

  it("renders WrittenContentAgent when selectedMode is writtenContent", () => {
    const { getByText } = render(<SelectedMode selectedMode="writtenContent" />);
    expect(getByText("Written Content Agent")).toBeInTheDocument();
  });

  it("renders nothing when selectedMode is helpMeChoose", () => {
    const { container } = render(<SelectedMode selectedMode="helpMeChoose" />);
    expect(container.firstChild).toBeNull();
  });
});
