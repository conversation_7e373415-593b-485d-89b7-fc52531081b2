import React, { useState } from "react";
import { But<PERSON> } from "@fluentui/react-components";
import { Icon24ArrowLeft } from "./Icons";
import { StartOptions } from "./StartOptions";
import { SelectedMode } from "./SelectedMode";
import { useStyles } from "./use-styles";
import { ChatMode } from "./chat-types";
import { Footer } from "./Footer";

const ChatPane = () => {
  const [selectedMode, setSelectedMode] = useState<ChatMode>();
  const { chatPane,btnWithIcon,btnIcon } = useStyles();


  const handleSelectMode = (mode: ChatMode) => {
    setSelectedMode(mode);
  };

  const handleBack = () => {
    setSelectedMode(undefined);
  };

  return (
    <div className={chatPane}>
      {selectedMode ? (
        <>
          <div style={{margin:"0 0 8px"}}>
            <Button className={btnWithIcon} icon={<Icon24ArrowLeft className={btnIcon}/>} onClick={handleBack} appearance="subtle">
                Choose another workflow
            </Button>
          </div>
          <SelectedMode selectedMode={selectedMode} />
        </>
      ) : (
        <StartOptions setSelectedMode={handleSelectMode} />
      )}

      <Footer />
    </div>
  );
};

export { ChatPane };
