import React from "react";
import { render, screen, fireEvent } from "@testing-library/react";
import { FluentProvider, webLightTheme } from "@fluentui/react-components";
import { StartOptions } from "../StartOptions";

jest.mock("launchdarkly-react-client-sdk", () => ({
  useFlags: () => ({
    writtenContent: true,
    lensDirector: true,
  }),
}));

jest.mock("../BotMessage", () => ({
  BotMessage: ({ message, hideAiWarning }: { message: string; hideAiWarning?: boolean }) => (
    <div data-testid="bot-message">
      {message}
      {hideAiWarning && <span data-testid="hide-ai-warning">hideAiWarning</span>}
    </div>
  ),
}));

jest.mock("../WhatDoYouWantButton", () => ({
  WhatDoYouWantButton: ({
    header,
    text,
    disabled,
    onClick,
  }: {
    header: string;
    text: string;
    disabled?: boolean;
    onClick: () => void;
  }) => (
    <button data-testid={`button-${header.toLowerCase().replace(/\s+/g, "-")}`} disabled={disabled} onClick={onClick}>
      {header}: {text}
    </button>
  ),
}));

jest.mock("../Icons", () => ({
  Icon24Valuation: () => <div data-testid="valuation-icon">💰</div>,
  Icon24Question: () => <div data-testid="question-icon">❓</div>,
  Icon24Data: () => <div data-testid="data-icon">📊</div>,
  Icon24Pencil: () => <div data-testid="pencil-icon">✏️</div>,
}));

const renderWithProvider = (component: React.ReactElement) => {
  return render(<FluentProvider theme={webLightTheme}>{component}</FluentProvider>);
};

describe("StartOptions", () => {
  const mockSetSelectedMode = jest.fn();

  beforeEach(() => {
    mockSetSelectedMode.mockClear();
  });

  it("renders welcome message with hideAiWarning", () => {
    renderWithProvider(<StartOptions setSelectedMode={mockSetSelectedMode} />);

    expect(screen.getByTestId("bot-message")).toBeInTheDocument();
    expect(screen.getByText("Welcome, where would you like to start today?")).toBeInTheDocument();
    expect(screen.getByTestId("hide-ai-warning")).toBeInTheDocument();
  });

  it("renders all workflow buttons", () => {
    renderWithProvider(<StartOptions setSelectedMode={mockSetSelectedMode} />);

    expect(screen.getByTestId("button-valuations")).toBeInTheDocument();
    expect(screen.getByTestId("button-lens-direct")).toBeInTheDocument();
    expect(screen.getByTestId("button-written-content")).toBeInTheDocument();
    expect(screen.getByTestId("button-help-me-find-answers")).toBeInTheDocument();
  });

  it("calls setSelectedMode with correct value when valuations button is clicked", () => {
    renderWithProvider(<StartOptions setSelectedMode={mockSetSelectedMode} />);

    const valuationsButton = screen.getByTestId("button-valuations");
    fireEvent.click(valuationsButton);

    expect(mockSetSelectedMode).toHaveBeenCalledWith("valuations");
  });

  it("calls setSelectedMode with correct value when lens direct button is clicked", () => {
    renderWithProvider(<StartOptions setSelectedMode={mockSetSelectedMode} />);

    const lensDirectButton = screen.getByTestId("button-lens-direct");
    fireEvent.click(lensDirectButton);

    expect(mockSetSelectedMode).toHaveBeenCalledWith("lensDirect");
  });

  it("calls setSelectedMode with correct value when written content button is clicked", () => {
    renderWithProvider(<StartOptions setSelectedMode={mockSetSelectedMode} />);

    const writtenContentButton = screen.getByTestId("button-written-content");
    fireEvent.click(writtenContentButton);

    expect(mockSetSelectedMode).toHaveBeenCalledWith("writtenContent");
  });

  it("calls setSelectedMode with correct value when help button is clicked", () => {
    renderWithProvider(<StartOptions setSelectedMode={mockSetSelectedMode} />);

    const helpButton = screen.getByTestId("button-help-me-find-answers");
    expect(helpButton).toBeDisabled();

    // Since the button is disabled, we can't test the click functionality
    // but we can verify the button renders with the correct text
    expect(helpButton).toBeInTheDocument();
  });
});
