AWSTemplateFormatVersion: "2010-09-09"
Description: Valuations Assistant Orchestrator

Parameters:
  Contact:
    Description: Tag - Contact for stack owner(s).
    Type: String
    Default: '<EMAIL>'
    ConstraintDescription: Must be a valid email address
  ProjectCode:
    Description: Tag - Project code the resources should be associated with.
    Type: String
    MinLength: 1 # mandatory parameter
    Default: DEV-VAL-SERV
  ProductCode:
    Description: Tag - Product code the resources should be associated with.
    Type: String
    MinLength: 1 # mandatory parameter
    Default: wmlgv
  BusinessUnit:
    Description: Tag - Business unit the resources should be associated with.
    Type: String
    Default: "Woodmac"
  Application:
    Description: Application code
    Type: String
    Default: 'valuations-ai'
  Environment:
    Description: Tag - Environment the resources should be associated with - this will automatically identify the correct Internal Frontend Cluster to use.
    Type: String
    Default: "aidev"
    AllowedValues: ["aidev", "aiint", "aiuat", "aiprod"]
  ProjectPrefix:
    Description: The project prefix
    Type: String
    Default: wmlgv
  ServiceName:
    Description: The service name associated with this haproxy task (typically name of the app served via haproxy); this will also be used to build the user-friendly DNS name mapped to the haproxy loadbalancer
    Type: String
    Default: "wmlgv-assistant-orchestrator"
  EcrImage:
    Description: The docker image to be used - the default value here is most likely correct
    Type: String
  BedrockAgentId:
    Description: The agent id to use
    Type: String
    Default: "ITOR9EQ6UY"
  BedrockAgentAliasId:
    Description: The agent alias id to use or the deafult value below. Default TSTALIASID is the working draft
    Type: String
    Default: "TSTALIASID"
  TokenIssuer:
    Description: the okta auth server
    Type: String
    Default: "https://woodmackenzie.okta.com/oauth2/default"
  TokenAudience:
    Description: the aud value present in access tokens
    Type: String
    Default: "api://default"
  LensIdentityUrl:
    Description: the lens identity url
    Type: String
    Default: https://identity.woodmac.com
  LensDigitalContentUrl:
    Description: the lens digital content url
    Type: String
    Default: https://api2.woodmac.com/chatbot-api
  LensUsername:
    Description: username of the user to obtain token for
    Type: String
  LensPassword:
    Description: password of the user to obtain token for
    Type: String
  UseEntitlements:
    Description: Feature flag to enable entitlements checking
    Type: String
    Default: "false"
    AllowedValues: ["true", "false"]
  EntitlementsOauthUrl:
    Description: OAuth2 token endpoint for entitlements service
    Type: String
  EntitlementsClientId:
    Description: OAuth2 client ID for entitlements service
    Type: String
  EntitlementsClientSecret:
    Description: OAuth2 client secret for entitlements service
    Type: String
    NoEcho: true
  EntitlementsApiUrl:
    Description: Entitlements API endpoint
    Type: String

Conditions:
  IsUserFacing: !Or [
    !Equals [!Ref Environment, "aiint"], # internal users (for now)
    !Equals [!Ref Environment, "aiprod"] # external users
  ]
  IsProd: !Equals [!Ref Environment, aiprod]

Resources:
  ##############################################################################
  # Internal Frontend Cluster
  ##############################################################################

  ValAssistantApiCluster:
    Type: AWS::ECS::Cluster
    Properties:
      ClusterName: !Join ["-", ["wmlgv-assistant-cluster", !Ref Environment]]
      ClusterSettings:
        - Name: containerInsights
          Value: enhanced
      Tags:
        - Key: BusinessUnit
          Value: !Ref BusinessUnit
        - Key: Contact
          Value: !Ref Contact
        - Key: Environment
          Value: !Ref Environment
        - Key: ProductCode
          Value: !Ref ProductCode
        - Key: ProjectCode
          Value: !Ref ProjectCode
        - Key: ProjectPrefix
          Value: !Ref ProjectPrefix
        - Key: Application
          Value: !Ref Application
        - !If
          - IsProd
          - Key: wm-splunk-metrics-enabled
            Value: true
          - !Ref "AWS::NoValue"

  ##############################################################################
  # Retrieve custom mappings for this environment and region.
  ##############################################################################

  CustomMapping:
    Type: Custom::Lookup
    Properties:
      ServiceToken: !Join
        - ":"
        - - "arn:aws:lambda"
          - !Ref "AWS::Region"
          - !Ref "AWS::AccountId"
          - "function:liveservices-environment-mappings-lambda"
      environment: !Ref Environment
      region: !Ref "AWS::Region"

  ##############################################################################
  # service
  ##############################################################################

  ExecutionRole:
    Type: AWS::IAM::Role
    Properties:
      RoleName: !Join ["-", ["role", !Ref ServiceName, !Ref Environment, "ExecutionRole"]]
      AssumeRolePolicyDocument:
        Statement:
          - Effect: Allow
            Principal:
              Service: ecs-tasks.amazonaws.com
            Action: "sts:AssumeRole"
      ManagedPolicyArns:
        - "arn:aws:iam::aws:policy/service-role/AmazonECSTaskExecutionRolePolicy"
      PermissionsBoundary: !Sub "arn:aws:iam::${AWS::AccountId}:policy/VA-PB-Standard"
      Tags:
        - Key: BusinessUnit
          Value: !Ref BusinessUnit
        - Key: Contact
          Value: !Ref Contact
        - Key: Environment
          Value: !Ref Environment
        - Key: ProductCode
          Value: !Ref ProductCode
        - Key: ProjectCode
          Value: !Ref ProjectCode
        - Key: ProjectPrefix
          Value: !Ref ProjectPrefix
        - Key: Application
          Value: !Ref Application

  ValAssistTaskDefinition:
    Type: AWS::ECS::TaskDefinition
    Properties:
      Family: !Ref ServiceName
      NetworkMode: awsvpc
      RequiresCompatibilities:
        - FARGATE
      Cpu: 256
      Memory: 512
      ExecutionRoleArn: !GetAtt ExecutionRole.Arn
      TaskRoleArn: !Ref TaskRole
      ContainerDefinitions:
        - Name: !Join ["-", ["service", !Ref ServiceName, !Ref Environment]]
          Image: !Ref EcrImage
          Environment:
            - Name: SERVER_PORT
              Value: "80"
            - Name: AGENT_ID
              Value: !Ref "BedrockAgentId"
            - Name: AGENT_ALIAS
              Value: !Ref "BedrockAgentAliasId"
            - Name: TOKEN_ISSUER
              Value: !Ref TokenIssuer
            - Name: TOKEN_AUDIENCE
              Value: !Ref TokenAudience
            - Name: ENVIRONMENT
              Value: !Ref Environment
            - Name: LENS_IDENTITY_URL
              Value: !Ref LensIdentityUrl
            - Name: LENS_DIGITAL_CONTENT_CHAT_BOT_URL
              Value: !Ref LensDigitalContentUrl
            - Name: LENS_USERNAME
              Value: !Ref LensUsername
            - Name: LENS_PASSWORD
              Value: !Ref LensPassword
            - Name: USE_ENTITLEMENTS
              Value: !Ref UseEntitlements
            - Name: ENTITLEMENTS_OAUTH_URL
              Value: !Ref EntitlementsOauthUrl
            - Name: ENTITLEMENTS_CLIENT_ID
              Value: !Ref EntitlementsClientId
            - Name: ENTITLEMENTS_CLIENT_SECRET
              Value: !Ref EntitlementsClientSecret
            - Name: ENTITLEMENTS_API_URL
              Value: !Ref EntitlementsApiUrl
            # Splunk OpenTelemetry
            - Name: OTEL_SERVICE_NAME
              Value: !Sub "${ServiceName}-${Environment}"
            - Name: OTEL_RESOURCE_ATTRIBUTES
              Value: !Sub "deployment.environment=${Environment}"
            - Name: OTEL_EXPORTER_OTLP_TRACES_PROTOCOL
              Value: "http/protobuf"
            # todo replace with internal collector once it works
            - Name: SPLUNK_ACCESS_TOKEN
              Value: "O2wm5i_ThElqz81jJ0INxA"
            - Name: SPLUNK_REALM
              Value: "eu0"
            # - Name: OTEL_EXPORTER_OTLP_ENDPOINT
            #   Value: !Sub "https://otel-collector.${Environment}.woodmac.com"
            # Disable logs and metrics collection - traces only
            - Name: OTEL_LOGS_EXPORTER
              Value: "none"
            - Name: OTEL_METRICS_EXPORTER
              Value: "none"
          PortMappings:
            - ContainerPort: "80"
          LogConfiguration:
            LogDriver: awslogs
            Options:
              awslogs-region: !Ref AWS::Region
              awslogs-group: !Ref LogGroup
              awslogs-stream-prefix: ecs
      Tags:
        - Key: BusinessUnit
          Value: !Ref BusinessUnit
        - Key: Contact
          Value: !Ref Contact
        - Key: Environment
          Value: !Ref Environment
        - Key: ProductCode
          Value: !Ref ProductCode
        - Key: ProjectCode
          Value: !Ref ProjectCode
        - Key: ProjectPrefix
          Value: !Ref ProjectPrefix
        - Key: Application
          Value: !Ref Application

  TaskRole:
    Type: AWS::IAM::Role
    Properties:
      RoleName: !Join ["-", ["role", !Ref ServiceName, !Ref Environment, "TaskRole"]]
      AssumeRolePolicyDocument:
        Statement:
          - Effect: Allow
            Principal:
              Service: ecs-tasks.amazonaws.com
            Action: "sts:AssumeRole"
      PermissionsBoundary: !Sub "arn:aws:iam::${AWS::AccountId}:policy/VA-PB-Standard"
      Policies:
        - PolicyName: !Sub wmlgv-assistant-task-role-policy-${Environment}
          PolicyDocument:
            Version: "2012-10-17"
            Statement:
              - Effect: Allow
                Action:
                  - bedrock:InvokeAgent
                  - bedrock:ListAgentAliases
                Resource: "*"
      Tags:
        - Key: BusinessUnit
          Value: !Ref BusinessUnit
        - Key: Contact
          Value: !Ref Contact
        - Key: Environment
          Value: !Ref Environment
        - Key: ProductCode
          Value: !Ref ProductCode
        - Key: ProjectCode
          Value: !Ref ProjectCode
        - Key: ProjectPrefix
          Value: !Ref ProjectPrefix
        - Key: Application
          Value: !Ref Application

  Service:
    Type: AWS::ECS::Service
    # This dependency is needed so that the load balancer is setup correctly in time
    DependsOn:
      - ListenerHTTPS
    Properties:
      ServiceName: !Join ["-", ["service", !Ref ServiceName, !Ref Environment]]
      Cluster: !Ref ValAssistantApiCluster
      TaskDefinition: !Ref ValAssistTaskDefinition
      PropagateTags: TASK_DEFINITION
      DeploymentConfiguration:
        MinimumHealthyPercent: 100
        MaximumPercent: 200
      DesiredCount: !If [IsUserFacing, 2, 1]
      # This may need to be adjusted if the container takes a while to start up
      HealthCheckGracePeriodSeconds: 30
      LaunchType: FARGATE
      NetworkConfiguration:
        AwsvpcConfiguration:
          AssignPublicIp: DISABLED
          Subnets:
            - !GetAtt CustomMapping.privateWebSubnet1
            - !GetAtt CustomMapping.privateWebSubnet2
          SecurityGroups:
            - !Ref ContainerSecurityGroup
      LoadBalancers:
        - ContainerName: !Join ["-", ["service", !Ref ServiceName, !Ref Environment]]
          ContainerPort: "80"
          TargetGroupArn: !Ref TargetGroup
      Tags:
        - Key: BusinessUnit
          Value: !Ref BusinessUnit
        - Key: Contact
          Value: !Ref Contact
        - Key: Environment
          Value: !Ref Environment
        - Key: ProductCode
          Value: !Ref ProductCode
        - Key: ProjectCode
          Value: !Ref ProjectCode
        - Key: ProjectPrefix
          Value: !Ref ProjectPrefix
        - Key: Application
          Value: !Ref Application

  TargetGroup:
    Type: AWS::ElasticLoadBalancingV2::TargetGroup
    Properties:
      HealthCheckIntervalSeconds: 10
      HealthCheckPath: /health
      HealthCheckTimeoutSeconds: 5
      UnhealthyThresholdCount: 2
      HealthyThresholdCount: 2
      Matcher:
        HttpCode: "200-499" # we accept a 5xx as valid response
      Name: !Join ["-", ["tg", "wmlgv-assistant", !Ref Environment]]
      Port: "80"
      Protocol: HTTP
      TargetGroupAttributes:
        - Key: deregistration_delay.timeout_seconds
          Value: 60 # default is 300
      TargetType: ip
      VpcId: !GetAtt CustomMapping.vpc
      Tags:
        - Key: BusinessUnit
          Value: !Ref BusinessUnit
        - Key: Contact
          Value: !Ref Contact
        - Key: Environment
          Value: !Ref Environment
        - Key: ProductCode
          Value: !Ref ProductCode
        - Key: ProjectCode
          Value: !Ref ProjectCode
        - Key: ProjectPrefix
          Value: !Ref ProjectPrefix
        - Key: Application
          Value: !Ref Application

  ListenerHTTPS:
    Type: AWS::ElasticLoadBalancingV2::Listener
    Properties:
      DefaultActions:
        - TargetGroupArn: !Ref TargetGroup
          Type: forward
      LoadBalancerArn: !Ref LoadBalancer
      Port: "443"
      Protocol: HTTPS
      Certificates:
        - CertificateArn:
            !Join [
              "",
              [
                "arn:aws:acm:",
                !Ref "AWS::Region",
                ":",
                !Ref "AWS::AccountId",
                ":certificate/",
                !GetAtt CustomMapping.sslCertificateId,
              ],
            ]

  ListenerHTTP:
    Type: AWS::ElasticLoadBalancingV2::Listener
    Properties:
      DefaultActions:
        - TargetGroupArn: !Ref TargetGroup
          Type: forward
      LoadBalancerArn: !Ref LoadBalancer
      Port: "80"
      Protocol: HTTP

  LoadBalancer:
    Type: AWS::ElasticLoadBalancingV2::LoadBalancer
    Properties:
      LoadBalancerAttributes:
        - Key: idle_timeout.timeout_seconds
          Value: 60
      Name: !Join ["-", ["lb", "wmlgv-assistant", !Ref Environment]]
      Scheme: internal
      SecurityGroups:
        - !Ref LoadBalancerSecurityGroup
      Subnets:
        - !GetAtt CustomMapping.privateWebSubnet1
        - !GetAtt CustomMapping.privateWebSubnet2
      Tags:
        - Key: BusinessUnit
          Value: !Ref BusinessUnit
        - Key: Contact
          Value: !Ref Contact
        - Key: Environment
          Value: !Ref Environment
        - Key: ProductCode
          Value: !Ref ProductCode
        - Key: ProjectCode
          Value: !Ref ProjectCode
        - Key: ProjectPrefix
          Value: !Ref ProjectPrefix
        - Key: Application
          Value: !Ref Application

  ContainerSecurityGroup:
    Type: AWS::EC2::SecurityGroup
    Properties:
      GroupDescription: !Join ["-", ["sg", !Ref ServiceName, !Ref Environment]]
      VpcId: !GetAtt CustomMapping.vpc
      SecurityGroupIngress:
        - IpProtocol: tcp
          FromPort: "80"
          ToPort: "80"
          SourceSecurityGroupId: !Ref LoadBalancerSecurityGroup
      Tags:
        - Key: BusinessUnit
          Value: !Ref BusinessUnit
        - Key: Contact
          Value: !Ref Contact
        - Key: Environment
          Value: !Ref Environment
        - Key: ProductCode
          Value: !Ref ProductCode
        - Key: ProjectCode
          Value: !Ref ProjectCode
        - Key: ProjectPrefix
          Value: !Ref ProjectPrefix
        - Key: Application
          Value: !Ref Application

  LoadBalancerSecurityGroup:
    Type: AWS::EC2::SecurityGroup
    Properties:
      GroupDescription: !Join ["-", ["sg-lb", !Ref ServiceName, !Ref Environment]]
      VpcId: !GetAtt CustomMapping.vpc
      SecurityGroupIngress:
        - IpProtocol: tcp
          FromPort: "443"
          ToPort: "443"
          CidrIp: 10.0.0.0/8
        - IpProtocol: tcp
          FromPort: "80"
          ToPort: "80"
          CidrIp: 10.0.0.0/8
      Tags:
        - Key: BusinessUnit
          Value: !Ref BusinessUnit
        - Key: Contact
          Value: !Ref Contact
        - Key: Environment
          Value: !Ref Environment
        - Key: ProductCode
          Value: !Ref ProductCode
        - Key: ProjectCode
          Value: !Ref ProjectCode
        - Key: ProjectPrefix
          Value: !Ref ProjectPrefix
        - Key: Application
          Value: !Ref Application

  ValAssistPublicRecordSet:
    Type: AWS::Route53::RecordSet
    Properties:
      HostedZoneId: !GetAtt CustomMapping.publicHostedZoneId
      Name: !Join [".", [!Ref ServiceName, !Ref Environment, "woodmac.com"]]
      Type: A
      AliasTarget:
        DNSName: !GetAtt LoadBalancer.DNSName
        HostedZoneId: !GetAtt LoadBalancer.CanonicalHostedZoneID

  ValAssistPrivateRecordSet:
    Type: AWS::Route53::RecordSet
    Properties:
      HostedZoneId: !GetAtt CustomMapping.privateHostedZoneId
      Name: !Join [".", [!Ref ServiceName, !Ref Environment, "woodmac.com"]]
      Type: A
      AliasTarget:
        DNSName: !GetAtt LoadBalancer.DNSName
        HostedZoneId: !GetAtt LoadBalancer.CanonicalHostedZoneID

  LogGroup:
    Type: AWS::Logs::LogGroup
    Properties:
      LogGroupName: !Join ["", [/ecs/, !Ref ServiceName, "-", !Ref Environment]]
      Tags:
        - Key: BusinessUnit
          Value: !Ref BusinessUnit
        - Key: Contact
          Value: !Ref Contact
        - Key: Environment
          Value: !Ref Environment
        - Key: ProductCode
          Value: !Ref ProductCode
        - Key: ProjectCode
          Value: !Ref ProjectCode
        - Key: ProjectPrefix
          Value: !Ref ProjectPrefix
        - Key: Application
          Value: !Ref Application
  
  AlbArnSSMParam:
    Type: AWS::SSM::Parameter
    Properties:
      Description: DO NOT UPDATE. Updated from CFN
      Name: !Join ["", ["/", !Ref Environment, "/", !Ref ServiceName, "/", "alb-arn" ]]
      Type: String
      Value: !Ref LoadBalancer

  OktaClientSecret:
    Type: AWS::SecretsManager::Secret
    Properties:
      Name: !Sub "wmlgv-assistant-orchestrator-${Environment}/okta-client-secret"
      Description: "okta client secret for getting token for entitlements."
      SecretString: "secret-value-to-be-updated"
  

##############################################################################
# Outputs:
##############################################################################

Outputs:
  StackNameId:
    Description: The name of the stack that was just run.
    Value: !Ref AWS::StackName
  StackId:
    Description: The stack id of the stack that was just created.
    Value: !Ref AWS::StackId
  RegionId:
    Description: The region in which this stack was just created.
    Value: !Ref AWS::Region
  AlbArn:
    Description: The region in which this stack was just created.
    Value: !Ref LoadBalancer
