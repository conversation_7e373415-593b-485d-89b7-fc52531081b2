import { renderHook, act, waitFor } from "@testing-library/react";
import { useWrittenContentAgent } from "../use-written-content-agent";
import { apiClient } from "../../api-client";

jest.mock("../../api-client");

const mockApiClient = apiClient as jest.Mocked<typeof apiClient>;

// eslint-disable-next-line @typescript-eslint/no-extraneous-class
global.TextDecoderStream = class {} as any;

describe("useWrittenContentAgent", () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("initializes with correct default state", () => {
    const { result } = renderHook(() => useWrittenContentAgent());

    expect(result.current.chatHistory).toEqual([]);
    expect(result.current.loading).toBe(false);
    expect(result.current.currentMessage).toBe("");
    expect(typeof result.current.handleChatMessageEntered).toBe("function");
  });

  it("handles successful streaming response with text only", async () => {
    const mockReader = {
      read: jest
        .fn()
        .mockResolvedValueOnce({
          value: 'event: data\ndata: {"type": "text", "content": ["Hello"]}\n\n',
          done: false,
        })
        .mockResolvedValueOnce({
          value: 'event: data\ndata: {"type": "text", "content": [" world"]}\n\n',
          done: false,
        })
        .mockResolvedValueOnce({
          value: "event: end\n\n",
          done: true,
        }),
    };

    const mockStream = {
      pipeThrough: jest.fn().mockReturnValue({
        getReader: jest.fn().mockReturnValue(mockReader),
      }),
    };

    mockApiClient.post.mockResolvedValue({
      status: 200,
      data: mockStream,
    });

    const { result } = renderHook(() => useWrittenContentAgent());

    await act(async () => {
      result.current.handleChatMessageEntered("test question");
    });

    await waitFor(() => {
      expect(result.current.chatHistory).toHaveLength(2);
    });

    expect(result.current.chatHistory[0]).toEqual({
      role: "user",
      message: "test question",
    });
    expect(result.current.chatHistory[1]).toEqual({
      role: "assistant",
      message: "Hello world",
      additionalData: "",
    });
    expect(result.current.loading).toBe(false);
    expect(result.current.currentMessage).toBe("");
  });

  it("handles streaming response with references", async () => {
    const mockReferences = [
      { tag: 1, title: "Test Document", section: { name: "Introduction" } },
      { tag: 2, title: "Reference Doc", section: { name: "Chapter 1" } },
    ];

    const mockReader = {
      read: jest
        .fn()
        .mockResolvedValueOnce({
          value: 'event: data\ndata: {"type": "text", "content": ["Test response"]}\n\n',
          done: false,
        })
        .mockResolvedValueOnce({
          value: `event: data\ndata: {"type": "reference", "content": ${JSON.stringify(mockReferences)}}\n\n`,
          done: false,
        })
        .mockResolvedValueOnce({
          value: "event: end\n\n",
          done: true,
        }),
    };

    const mockStream = {
      pipeThrough: jest.fn().mockReturnValue({
        getReader: jest.fn().mockReturnValue(mockReader),
      }),
    };

    mockApiClient.post.mockResolvedValue({
      status: 200,
      data: mockStream,
    });

    const { result } = renderHook(() => useWrittenContentAgent());

    await act(async () => {
      result.current.handleChatMessageEntered("test question");
    });

    await waitFor(() => {
      expect(result.current.chatHistory).toHaveLength(2);
    });

    expect(result.current.chatHistory[1]).toEqual({
      role: "assistant",
      message: "Test response",
      additionalData: "References:\n1: Test Document: Introduction\n2: Reference Doc: Chapter 1",
    });
  });

  it("handles API errors gracefully", async () => {
    mockApiClient.post.mockRejectedValue(new Error("Network error"));

    const { result } = renderHook(() => useWrittenContentAgent());

    await act(async () => {
      result.current.handleChatMessageEntered("test question");
    });

    await waitFor(() => {
      expect(result.current.chatHistory).toHaveLength(2);
    });

    expect(result.current.chatHistory[1]).toEqual({
      role: "assistant",
      message: "Sorry, something has gone wrong. Please ask me something else",
    });
    expect(result.current.loading).toBe(false);
    expect(result.current.currentMessage).toBe("");
  });

  it("handles 4xx/5xx HTTP errors", async () => {
    const mockErrorStream = {
      text: jest.fn().mockResolvedValue('{"message": "Bad request"}'),
    };

    mockApiClient.post.mockResolvedValue({
      status: 400,
      headers: { "content-type": "application/json" },
      data: mockErrorStream,
    });

    const { result } = renderHook(() => useWrittenContentAgent());

    await act(async () => {
      result.current.handleChatMessageEntered("test question");
    });

    await waitFor(() => {
      expect(result.current.chatHistory).toHaveLength(2);
    });

    expect(result.current.chatHistory[1]).toEqual({
      role: "assistant",
      message: "Sorry, something has gone wrong. Please ask me something else",
    });
  });

  it("ignores empty or whitespace-only messages", async () => {
    const { result } = renderHook(() => useWrittenContentAgent());

    await act(async () => {
      result.current.handleChatMessageEntered("   ");
    });

    expect(result.current.chatHistory).toHaveLength(0);
    expect(mockApiClient.post).not.toHaveBeenCalled();
  });

  it("sets loading state during message processing", async () => {
    let resolvePromise: (value: any) => void;
    const promise = new Promise((resolve) => {
      resolvePromise = resolve;
    });

    mockApiClient.post.mockReturnValue(promise as any);

    const { result } = renderHook(() => useWrittenContentAgent());

    act(() => {
      result.current.handleChatMessageEntered("test message");
    });

    expect(result.current.loading).toBe(true);

    const mockReader = {
      read: jest.fn().mockResolvedValue({ value: "", done: true }),
    };

    const mockStream = {
      pipeThrough: jest.fn().mockReturnValue({
        getReader: jest.fn().mockReturnValue(mockReader),
      }),
    };

    await act(async () => {
      resolvePromise({
        status: 200,
        data: mockStream,
      });
      await promise;
    });

    expect(result.current.loading).toBe(false);
  });

  it("updates current message during streaming", async () => {
    const mockReader = {
      read: jest
        .fn()
        .mockResolvedValueOnce({
          value: 'event: data\ndata: {"type": "text", "content": ["Hello"]}\n\n',
          done: false,
        })
        .mockResolvedValueOnce({
          value: "event: end\n\n",
          done: true,
        }),
    };

    const mockStream = {
      pipeThrough: jest.fn().mockReturnValue({
        getReader: jest.fn().mockReturnValue(mockReader),
      }),
    };

    mockApiClient.post.mockResolvedValue({
      status: 200,
      data: mockStream,
    });

    const { result } = renderHook(() => useWrittenContentAgent());

    await act(async () => {
      result.current.handleChatMessageEntered("test question");
    });

    await waitFor(() => {
      expect(result.current.chatHistory).toHaveLength(2);
    });

    expect(result.current.currentMessage).toBe("");
  });

  it("handles malformed JSON in stream gracefully", async () => {
    const mockReader = {
      read: jest
        .fn()
        .mockResolvedValueOnce({
          value: "event: data\ndata: invalid json\n\n",
          done: false,
        })
        .mockResolvedValueOnce({
          value: "event: end\n\n",
          done: true,
        }),
    };

    const mockStream = {
      pipeThrough: jest.fn().mockReturnValue({
        getReader: jest.fn().mockReturnValue(mockReader),
      }),
    };

    mockApiClient.post.mockResolvedValue({
      status: 200,
      data: mockStream,
    });

    const { result } = renderHook(() => useWrittenContentAgent());

    await act(async () => {
      result.current.handleChatMessageEntered("test question");
    });

    await waitFor(() => {
      expect(result.current.chatHistory).toHaveLength(2);
    });

    expect(result.current.chatHistory[1]).toEqual({
      role: "assistant",
      message: "",
      additionalData: "",
    });
  });
});
