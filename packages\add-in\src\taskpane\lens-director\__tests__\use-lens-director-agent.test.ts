import { renderHook, act, waitFor } from "@testing-library/react";
import { useLensDirectorAgent } from "../use-lens-director-agent";
import { apiClient } from "../../api-client";
import { useFlags } from "launchdarkly-react-client-sdk";

jest.mock("../../api-client");
jest.mock("launchdarkly-react-client-sdk");

const mockApiClient = apiClient as jest.Mocked<typeof apiClient>;
const mockUseFlags = useFlags as jest.MockedFunction<typeof useFlags>;

global.Excel = {
  run: jest.fn().mockImplementation((callback) => {
    const context = {
      workbook: {
        worksheets: {
          add: jest.fn().mockReturnValue({
            activate: jest.fn(),
            getUsedRange: jest.fn().mockReturnValue({
              clear: jest.fn(),
              format: {
                autofitColumns: jest.fn(),
              },
            }),
            getRange: jest.fn().mockReturnValue({
              getAbsoluteResizedRange: jest.fn().mockReturnValue({
                values: null,
              }),
            }),
            tables: {
              add: jest.fn(),
            },
          }),
        },
      },
      sync: jest.fn().mockResolvedValue(undefined),
    };
    return callback(context);
  }),
} as any;

describe("useLensDirectorAgent", () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mockUseFlags.mockReturnValue({ suggestQuestions: false });
  });

  it("initializes with correct default state", () => {
    const { result } = renderHook(() => useLensDirectorAgent());

    expect(result.current.chatHistory).toEqual([]);
    expect(result.current.loading).toBe(false);
    expect(result.current.loadingLensDirectData).toBe(false);
    expect(result.current.suggestedQuestions).toEqual([]);
    expect(result.current.showSuggestedQuestions).toBe(false);
    expect(typeof result.current.handleChatMessageEntered).toBe("function");
    expect(typeof result.current.loadLensDirectData).toBe("function");
  });

  it("loads suggested questions when flag is enabled", async () => {
    const mockQuestions = ["Question 1", "Question 2"];
    mockUseFlags.mockReturnValue({ suggestQuestions: true });
    mockApiClient.get.mockResolvedValue({
      data: { questions: mockQuestions },
    });

    const { result } = renderHook(() => useLensDirectorAgent());

    await waitFor(() => {
      expect(result.current.suggestedQuestions).toEqual(mockQuestions);
    });

    expect(mockApiClient.get).toHaveBeenCalledWith("lens-director/questions");
  });

  it("does not load suggested questions when flag is disabled", () => {
    mockUseFlags.mockReturnValue({ suggestQuestions: false });

    renderHook(() => useLensDirectorAgent());

    expect(mockApiClient.get).not.toHaveBeenCalled();
  });

  it("shows suggested questions when conditions are met", () => {
    mockUseFlags.mockReturnValue({ suggestQuestions: true });

    const { result } = renderHook(() => useLensDirectorAgent());

    expect(result.current.showSuggestedQuestions).toBe(true);
  });

  it("hides suggested questions when chat history exists", async () => {
    mockUseFlags.mockReturnValue({ suggestQuestions: true });
    mockApiClient.post.mockResolvedValue({
      data: {
        status: "Success",
        humanReadableResponse: "Test response",
        oDataBaseUrl: "https://api.test.com",
        oDataQuery: "test-query",
      },
    });

    const { result } = renderHook(() => useLensDirectorAgent());

    await act(async () => {
      await result.current.handleChatMessageEntered("test message");
    });

    expect(result.current.showSuggestedQuestions).toBe(false);
  });

  it("handles successful chat message", async () => {
    const mockResponse = {
      status: "Success",
      humanReadableResponse: "Here is your data",
      oDataBaseUrl: "https://api.test.com",
      oDataQuery: "users?$select=name,age",
    };

    mockApiClient.post.mockResolvedValue({ data: mockResponse });

    const { result } = renderHook(() => useLensDirectorAgent());

    await act(async () => {
      await result.current.handleChatMessageEntered("Show me users");
    });

    expect(result.current.chatHistory).toHaveLength(2);
    expect(result.current.chatHistory[0]).toEqual({
      role: "user",
      message: "Show me users",
    });
    expect(result.current.chatHistory[1]).toEqual({
      role: "assistant",
      message:
        "Here is your data, I have generated this odata query for you:\n\nhttps://api.test.com/users?$select=name,age",
      additionalData: "https://api.test.com/users?$select=name,age",
    });
    expect(result.current.loading).toBe(false);
  });

  it("handles failed API response", async () => {
    const mockResponse = {
      status: "Error",
      error: "Something went wrong",
    };

    mockApiClient.post.mockResolvedValue({ data: mockResponse });

    const { result } = renderHook(() => useLensDirectorAgent());

    await act(async () => {
      await result.current.handleChatMessageEntered("test message");
    });

    expect(result.current.chatHistory).toHaveLength(2);
    expect(result.current.chatHistory[1]).toEqual({
      role: "assistant",
      message: "Sorry, something has gone wrong. Please ask me something else",
    });
    expect(result.current.loading).toBe(false);
  });

  it("handles network error", async () => {
    mockApiClient.post.mockRejectedValue(new Error("Network error"));

    const { result } = renderHook(() => useLensDirectorAgent());

    await act(async () => {
      await result.current.handleChatMessageEntered("test message");
    });

    expect(result.current.chatHistory).toHaveLength(2);
    expect(result.current.chatHistory[1]).toEqual({
      role: "assistant",
      message: "Sorry, something has gone wrong. Please ask me something else",
    });
    expect(result.current.loading).toBe(false);
  });

  it("ignores empty messages", async () => {
    const { result } = renderHook(() => useLensDirectorAgent());

    await act(async () => {
      await result.current.handleChatMessageEntered("   ");
    });

    expect(result.current.chatHistory).toHaveLength(0);
    expect(mockApiClient.post).not.toHaveBeenCalled();
  });

  it("sets loading state during message processing", async () => {
    let resolvePromise: (value: any) => void;
    const promise = new Promise((resolve) => {
      resolvePromise = resolve;
    });

    mockApiClient.post.mockReturnValue(promise as any);

    const { result } = renderHook(() => useLensDirectorAgent());

    act(() => {
      result.current.handleChatMessageEntered("test message");
    });

    expect(result.current.loading).toBe(true);

    await act(async () => {
      resolvePromise({
        data: {
          status: "Success",
          humanReadableResponse: "Test",
          oDataBaseUrl: "https://api.test.com",
          oDataQuery: "test",
        },
      });
      await promise;
    });

    expect(result.current.loading).toBe(false);
  });

  describe("loadLensDirectData", () => {
    it("loads data and creates Excel table with data", async () => {
      const mockData = {
        value: [
          { name: "John", age: 25 },
          { name: "Jane", age: 30 },
        ],
      };

      mockApiClient.post.mockResolvedValue({ data: mockData });

      const { result } = renderHook(() => useLensDirectorAgent());

      await act(async () => {
        await result.current.loadLensDirectData("https://api.test.com/users");
      });

      expect(mockApiClient.post).toHaveBeenCalledWith("lens-direct-data", {
        oDataUrl: "https://api.test.com/users",
      });
      expect(Excel.run).toHaveBeenCalled();
    });

    it("handles empty data response", async () => {
      const mockData = { value: [] };

      mockApiClient.post.mockResolvedValue({ data: mockData });

      const { result } = renderHook(() => useLensDirectorAgent());

      await act(async () => {
        await result.current.loadLensDirectData("https://api.test.com/empty");
      });

      expect(mockApiClient.post).toHaveBeenCalledWith("lens-direct-data", {
        oDataUrl: "https://api.test.com/empty",
      });
      expect(Excel.run).toHaveBeenCalled();
    });

    it("sets loading state during data loading", async () => {
      let resolvePromise: (value: any) => void;
      const promise = new Promise((resolve) => {
        resolvePromise = resolve;
      });

      mockApiClient.post.mockReturnValue(promise as any);

      const { result } = renderHook(() => useLensDirectorAgent());

      act(() => {
        result.current.loadLensDirectData("https://api.test.com/users");
      });

      expect(result.current.loadingLensDirectData).toBe(true);

      await act(async () => {
        resolvePromise({ data: { value: [] } });
        await promise;
      });

      expect(result.current.loadingLensDirectData).toBe(false);
    });
  });
});
