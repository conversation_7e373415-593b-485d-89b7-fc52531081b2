import React from "react";
import { render, screen } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import { LensDirectorAgent } from "../LensDirectorAgent";
import { useLensDirectorAgent } from "../use-lens-director-agent";

jest.mock("../use-lens-director-agent");
jest.mock("../../use-styles", () => ({
  useStyles: () => ({
    buttonIcon: "button-icon-class",
  }),
}));

jest.mock("../../Agent", () => ({
  Agent: ({
    howCanIHelpMessage,
    chatHistory,
    loading,
    handleChatMessageEntered,
    showSuggestedQuestions,
    suggestedQuestions,
    botMessageFooterComponentsCreator,
  }: any) => (
    <div data-testid="agent">
      <div data-testid="help-message">{howCanIHelpMessage}</div>
      <div data-testid="chat-history">{JSON.stringify(chatHistory)}</div>
      <div data-testid="loading">{loading.toString()}</div>
      <button data-testid="message-button" onClick={() => handleChatMessageEntered("test message")}>
        Send Message
      </button>
      <div data-testid="suggested-questions-flag">{showSuggestedQuestions.toString()}</div>
      <div data-testid="suggested-questions">{JSON.stringify(suggestedQuestions)}</div>
      {botMessageFooterComponentsCreator && (
        <div data-testid="footer-components">{botMessageFooterComponentsCreator("test-odata-url")}</div>
      )}
    </div>
  ),
}));

const mockUseLensDirectorAgent = useLensDirectorAgent as jest.MockedFunction<typeof useLensDirectorAgent>;

describe("LensDirectorAgent", () => {
  const mockProps = {
    chatHistory: [{ role: "user" as const, message: "test message" }],
    loading: false,
    handleChatMessageEntered: jest.fn(),
    showSuggestedQuestions: true,
    suggestedQuestions: ["Question 1", "Question 2"],
    loadLensDirectData: jest.fn(),
    loadingLensDirectData: false,
  };

  beforeEach(() => {
    jest.clearAllMocks();
    mockUseLensDirectorAgent.mockReturnValue(mockProps);
  });

  it("renders Agent component with correct props", () => {
    render(<LensDirectorAgent />);

    expect(screen.getByTestId("agent")).toBeInTheDocument();
    expect(screen.getByTestId("help-message")).toHaveTextContent("How can I help you with Lens Direct?");
    expect(screen.getByTestId("chat-history")).toHaveTextContent(JSON.stringify(mockProps.chatHistory));
    expect(screen.getByTestId("loading")).toHaveTextContent("false");
    expect(screen.getByTestId("suggested-questions-flag")).toHaveTextContent("true");
    expect(screen.getByTestId("suggested-questions")).toHaveTextContent(JSON.stringify(mockProps.suggestedQuestions));
  });

  it("handles chat message entered", async () => {
    const user = userEvent.setup();
    render(<LensDirectorAgent />);

    await user.click(screen.getByTestId("message-button"));

    expect(mockProps.handleChatMessageEntered).toHaveBeenCalledWith("test message");
  });

  it("renders footer components when table data is provided", () => {
    render(<LensDirectorAgent />);

    const footerComponents = screen.getByTestId("footer-components");
    expect(footerComponents).toBeInTheDocument();
  });

  it("renders LoadLensDirectDataButton in footer components", () => {
    render(<LensDirectorAgent />);

    const footerComponents = screen.getByTestId("footer-components");
    expect(footerComponents).toBeInTheDocument();
    expect(footerComponents.textContent).toContain("Load data to a new sheet");
  });

  it("handles loading state correctly", () => {
    mockUseLensDirectorAgent.mockReturnValue({
      ...mockProps,
      loading: true,
    });

    render(<LensDirectorAgent />);

    expect(screen.getByTestId("loading")).toHaveTextContent("true");
  });

  it("handles loadingLensDirectData state in button", () => {
    mockUseLensDirectorAgent.mockReturnValue({
      ...mockProps,
      loadingLensDirectData: true,
    });

    render(<LensDirectorAgent />);

    const footerComponents = screen.getByTestId("footer-components");
    expect(footerComponents).toBeInTheDocument();
  });
});
